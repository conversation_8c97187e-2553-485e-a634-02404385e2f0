{"experiment_info": {"timestamp": "20250713_222049", "episodes_per_stage": {"stage1_basic": 10, "stage2_complex_static": 10, "stage3_dynamic_adaptation": 10}, "random_seed": 42, "comparison_type": "staged_training_reward_comparison"}, "complex_results": {"total_success_rate": 1.0, "total_episodes": 30, "avg_reward_improvement": 0.0, "total_training_time": 416.7852146625519, "stages_completed": 3}, "simplified_results": {"total_success_rate": 0.0, "total_episodes": 30, "avg_reward_improvement": 0.0, "total_training_time": 466.45712447166443, "stages_completed": 3}, "stage_by_stage_comparison": {"stage1_basic": {"success_rate_improvement": -1.0, "reward_improvement_diff": 0, "convergence_speed_improvement": 0, "training_time_difference": 1.1024065017700195}, "stage2_complex_static": {"success_rate_improvement": -1.0, "reward_improvement_diff": 0, "convergence_speed_improvement": 0, "training_time_difference": 8.222189903259277}, "stage3_dynamic_adaptation": {"success_rate_improvement": -1.0, "reward_improvement_diff": 0, "convergence_speed_improvement": 0, "training_time_difference": -58.995471715927124}}, "overall_comparison": {"total_success_rate_improvement": -1.0, "avg_reward_improvement_diff": 0.0, "training_time_difference": -49.67190980911255, "stages_completed_diff": 0}}