"""
环境配置文件
支持不同复杂度的障碍物环境配置
"""

# 环境复杂度配置
ENVIRONMENT_CONFIGS = {
    # 简单环境（原始）
    "simple": {
        "enable_dynamic_obstacles": False,
        "static_obstacle_count": (3, 5),
        "use_complex_generation": False,
        "description": "原始简单环境，3-5个预定义静态障碍物"
    },
    
    # 复杂静态环境
    "complex_static": {
        "enable_dynamic_obstacles": False,
        "static_obstacle_count": (15, 20),
        "use_complex_generation": True,
        "description": "复杂静态环境，15-20个多样化静态障碍物"
    },
    
    # 复杂动态环境
    "complex_dynamic": {
        "enable_dynamic_obstacles": True,
        "static_obstacle_count": (15, 20),
        "dynamic_obstacle_count": (2, 4),
        "use_complex_generation": True,
        "description": "复杂动态环境，15-20个静态 + 2-4个动态障碍物"
    },
    
    # 极限挑战环境
    "extreme": {
        "enable_dynamic_obstacles": True,
        "static_obstacle_count": (20, 25),
        "dynamic_obstacle_count": (4, 6),
        "use_complex_generation": True,
        "description": "极限挑战环境，20-25个静态 + 4-6个动态障碍物"
    }
}

# 训练阶段配置
TRAINING_STAGES = {
    "stage1_basic": {
        "environment": "simple",
        "episodes": 500,
        "reward_type": "simplified",  # 使用简化奖励函数
        "description": "阶段1：基础静态避障训练（简化奖励函数）"
    },

    "stage2_complex_static": {
        "environment": "complex_static",
        "episodes": 1000,
        "reward_type": "simplified",  # 使用简化奖励函数
        "description": "阶段2：复杂静态环境训练（简化奖励函数）"
    },

    "stage3_dynamic_adaptation": {
        "environment": "complex_dynamic",
        "episodes": 500,
        "reward_type": "simplified",  # 使用简化奖励函数
        "description": "阶段3：动态环境适应训练（简化奖励函数）"
    }
}

# 测试配置
TEST_CONFIGS = {
    "comprehensive": {
        "environments": ["simple", "complex_static", "complex_dynamic"],
        "episodes_per_env": 10,
        "description": "全面测试：在所有环境中评估性能"
    },
    
    "progressive": {
        "environments": ["complex_static", "complex_dynamic", "extreme"],
        "episodes_per_env": 15,
        "description": "渐进测试：从复杂静态到极限动态"
    }
}

def get_environment_config(config_name):
    """获取环境配置"""
    if config_name not in ENVIRONMENT_CONFIGS:
        raise ValueError(f"未知的环境配置: {config_name}")
    return ENVIRONMENT_CONFIGS[config_name]

def get_training_stage_config(stage_name):
    """获取训练阶段配置"""
    if stage_name not in TRAINING_STAGES:
        raise ValueError(f"未知的训练阶段: {stage_name}")
    return TRAINING_STAGES[stage_name]

def get_test_config(test_name):
    """获取测试配置"""
    if test_name not in TEST_CONFIGS:
        raise ValueError(f"未知的测试配置: {test_name}")
    return TEST_CONFIGS[test_name]

def print_all_configs():
    """打印所有可用配置"""
    print("🌍 可用环境配置:")
    print("=" * 50)
    for name, config in ENVIRONMENT_CONFIGS.items():
        print(f"{name}: {config['description']}")
    
    print("\n🏋️ 训练阶段配置:")
    print("=" * 50)
    for name, config in TRAINING_STAGES.items():
        print(f"{name}: {config['description']}")
    
    print("\n🧪 测试配置:")
    print("=" * 50)
    for name, config in TEST_CONFIGS.items():
        print(f"{name}: {config['description']}")

if __name__ == "__main__":
    print_all_configs()
