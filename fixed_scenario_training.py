"""
固定场景训练脚本
实现用户建议的固定场景分阶段训练系统：
- 阶段1：5个固定障碍物（简单场景）
- 阶段2：15个固定障碍物（继承阶段1的5个 + 新增10个）
- 阶段3：15个固定静态 + 3个固定动态障碍物
确保不同奖励函数在完全相同的场景下训练，实现公平对比
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import json
import time
from datetime import datetime
import argparse

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dwa_rl_core import StabilizedEnvironment, StabilizedTD3Controller
from fixed_scenario_config import FIXED_SCENARIO, get_fixed_scenario_config
from td3_dwa_rl_architecture import td3_config

class FixedScenarioTraining:
    """固定场景分阶段训练系统"""
    
    def __init__(self, seed=42):
        self.seed = seed
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 分阶段训练配置
        self.training_stages = {
            'stage1': {'episodes': 500, 'scenario': 'stage1', 'description': '阶段1：简单场景训练'},
            'stage2': {'episodes': 1000, 'scenario': 'stage2', 'description': '阶段2：复杂静态场景训练'},
            'stage3': {'episodes': 500, 'scenario': 'stage3', 'description': '阶段3：复杂动态场景训练'}
        }
        
        # 创建输出目录
        self.output_dir = f'fixed_scenario_training_{self.timestamp}'
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("🎯 固定场景分阶段训练系统")
        print("=" * 80)
        print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎲 随机种子: {seed}")
        print()
        
        # 打印训练计划
        total_episodes = sum(stage['episodes'] for stage in self.training_stages.values())
        print("📋 训练计划:")
        for stage_name, config in self.training_stages.items():
            print(f"  • {config['description']}: {config['episodes']} episodes")
        print(f"  • 每种奖励函数总计: {total_episodes} episodes")
        print(f"  • 两种奖励函数总计: {total_episodes * 2} episodes")
        print("=" * 80)
        
        # 打印固定场景配置
        FIXED_SCENARIO.print_scenario_summary()
    
    def train_single_reward_type(self, reward_type='simplified'):
        """训练单个奖励函数类型的完整分阶段训练"""
        print(f"\n🚀 开始 {reward_type.upper()} 奖励函数分阶段训练")
        print("=" * 60)
        
        # 设置随机种子
        np.random.seed(self.seed)
        
        # 训练结果记录
        all_results = {
            'reward_type': reward_type,
            'stages': {},
            'total_episodes': 0,
            'total_training_time': 0,
            'overall_success_rate': 0
        }
        
        # 创建控制器（在所有阶段间保持连续性）
        controller = StabilizedTD3Controller(td3_config)
        
        # 逐阶段训练
        for stage_name, stage_config in self.training_stages.items():
            print(f"\n📍 {stage_config['description']}")
            print("-" * 40)
            
            # 获取该阶段的固定场景配置
            scenario_config = get_fixed_scenario_config(stage_config['scenario'])
            
            # 创建环境（使用固定场景配置）
            env = StabilizedEnvironment(
                enable_dynamic_obstacles=(len(scenario_config['dynamic_obstacles']) > 0),
                reward_type=reward_type,
                fixed_scenario_config=scenario_config
            )
            
            # 训练该阶段
            stage_results = self._train_stage(
                env, controller, 
                stage_config['episodes'], 
                stage_name, 
                reward_type
            )
            
            # 记录阶段结果
            all_results['stages'][stage_name] = stage_results
            all_results['total_episodes'] += stage_results['episodes']
            all_results['total_training_time'] += stage_results['training_time']
        
        # 计算总体成功率
        total_success = sum(stage['success_count'] for stage in all_results['stages'].values())
        all_results['overall_success_rate'] = total_success / all_results['total_episodes']
        
        # 保存结果
        self._save_training_results(all_results)
        
        # 生成可视化
        self._generate_training_visualization(all_results)
        
        print(f"\n✅ {reward_type.upper()} 奖励函数训练完成!")
        print(f"📊 总体成功率: {all_results['overall_success_rate']:.1%}")
        print(f"⏱️ 总训练时间: {all_results['total_training_time']:.1f}秒")
        
        return all_results, controller
    
    def _train_stage(self, env, controller, num_episodes, stage_name, reward_type):
        """训练单个阶段"""
        stage_results = {
            'stage_name': stage_name,
            'episodes': num_episodes,
            'episode_rewards': [],
            'episode_steps': [],
            'success_count': 0,
            'collision_count': 0,
            'timeout_count': 0,
            'training_time': 0
        }
        
        start_time = time.time()
        
        for episode in range(num_episodes):
            # 重置环境（使用固定场景）
            state = env.reset()
            full_state = np.concatenate([env.state, state[6:]])
            
            episode_reward = 0
            step_count = 0
            
            while step_count < 500:
                # 获取动作
                action, info, safe_actions = controller.get_action_with_quality(
                    full_state, env.goal, env.obstacles, add_noise=(episode > 10)
                )
                
                # 执行动作
                next_state, reward, done, info_step = env.step(action)
                next_full_state = np.concatenate([env.state, next_state[6:]])
                
                # 存储经验
                controller.replay_buffer.add(
                    full_state, action, reward, next_full_state, done,
                    safe_actions, env.goal, env.obstacles, info.get('action_idx', 0)
                )
                
                # 训练网络
                if len(controller.replay_buffer) > 1000 and episode > 10:
                    controller.train_step_improved()
                
                episode_reward += reward
                step_count += 1
                full_state = next_full_state
                
                if done:
                    break
            
            # 记录episode结果
            stage_results['episode_rewards'].append(episode_reward)
            stage_results['episode_steps'].append(step_count)
            
            # 判断episode结果类型
            if info_step.get('success', False):
                stage_results['success_count'] += 1
                result_type = "✅"
            elif info_step.get('collision', False):
                stage_results['collision_count'] += 1
                result_type = "💥"
            else:
                stage_results['timeout_count'] += 1
                result_type = "⏰"
            
            # 打印进度
            if (episode + 1) % 50 == 0:
                success_rate = stage_results['success_count'] / (episode + 1)
                avg_reward = np.mean(stage_results['episode_rewards'][-50:])
                print(f"  Episode {episode+1:4d}/{num_episodes}: {result_type} "
                      f"成功率={success_rate:.1%}, 平均奖励={avg_reward:.1f}")
        
        stage_results['training_time'] = time.time() - start_time
        
        # 打印阶段总结
        success_rate = stage_results['success_count'] / num_episodes
        print(f"\n📊 {stage_name} 训练完成:")
        print(f"  成功率: {success_rate:.1%} ({stage_results['success_count']}/{num_episodes})")
        print(f"  碰撞率: {stage_results['collision_count']/num_episodes:.1%}")
        print(f"  超时率: {stage_results['timeout_count']/num_episodes:.1%}")
        print(f"  平均奖励: {np.mean(stage_results['episode_rewards']):.2f}")
        print(f"  训练时间: {stage_results['training_time']:.1f}秒")
        
        return stage_results
    
    def _save_training_results(self, results):
        """保存训练结果"""
        filename = f"{self.output_dir}/training_results_{results['reward_type']}.json"
        
        # 转换numpy数组为列表以便JSON序列化
        results_copy = results.copy()
        for stage_name, stage_data in results_copy['stages'].items():
            stage_data['episode_rewards'] = [float(r) for r in stage_data['episode_rewards']]
            stage_data['episode_steps'] = [int(s) for s in stage_data['episode_steps']]
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results_copy, f, indent=2, ensure_ascii=False)
        
        print(f"💾 训练结果已保存: {filename}")
    
    def _generate_training_visualization(self, results):
        """生成训练可视化图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'{results["reward_type"].upper()} 奖励函数 - 固定场景分阶段训练结果', fontsize=16)
        
        # 1. 各阶段成功率对比
        stages = list(results['stages'].keys())
        success_rates = [results['stages'][stage]['success_count'] / results['stages'][stage]['episodes'] 
                        for stage in stages]
        
        axes[0, 0].bar(stages, success_rates, color=['lightblue', 'lightgreen', 'lightcoral'])
        axes[0, 0].set_title('各阶段成功率')
        axes[0, 0].set_ylabel('成功率')
        axes[0, 0].set_ylim(0, 1)
        for i, rate in enumerate(success_rates):
            axes[0, 0].text(i, rate + 0.02, f'{rate:.1%}', ha='center')
        
        # 2. 奖励曲线（分阶段显示）
        episode_offset = 0
        colors = ['blue', 'green', 'red']
        for i, (stage_name, stage_data) in enumerate(results['stages'].items()):
            episodes = range(episode_offset, episode_offset + len(stage_data['episode_rewards']))
            axes[0, 1].plot(episodes, stage_data['episode_rewards'], 
                           color=colors[i], alpha=0.7, label=stage_name)
            episode_offset += len(stage_data['episode_rewards'])
        
        axes[0, 1].set_title('训练奖励曲线（分阶段）')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('奖励')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 各阶段episode步数分布
        all_steps = []
        stage_labels = []
        for stage_name, stage_data in results['stages'].items():
            all_steps.extend(stage_data['episode_steps'])
            stage_labels.extend([stage_name] * len(stage_data['episode_steps']))
        
        # 创建箱线图数据
        steps_by_stage = [results['stages'][stage]['episode_steps'] for stage in stages]
        axes[1, 0].boxplot(steps_by_stage, labels=stages)
        axes[1, 0].set_title('各阶段Episode步数分布')
        axes[1, 0].set_ylabel('步数')
        
        # 4. 训练时间对比
        training_times = [results['stages'][stage]['training_time'] for stage in stages]
        axes[1, 1].bar(stages, training_times, color=['orange', 'purple', 'brown'])
        axes[1, 1].set_title('各阶段训练时间')
        axes[1, 1].set_ylabel('时间 (秒)')
        for i, time_val in enumerate(training_times):
            axes[1, 1].text(i, time_val + max(training_times)*0.02, f'{time_val:.1f}s', ha='center')
        
        plt.tight_layout()
        
        # 保存图表
        plot_filename = f"{self.output_dir}/training_visualization_{results['reward_type']}.png"
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        print(f"📈 训练可视化已保存: {plot_filename}")
        
        plt.show()
        
        return fig

    def run_complete_comparison(self):
        """运行完整的奖励函数对比训练"""
        print("\n🔬 开始完整的固定场景奖励函数对比实验")
        print("=" * 80)

        # 训练两种奖励函数
        simplified_results, simplified_controller = self.train_single_reward_type('simplified')
        complex_results, complex_controller = self.train_single_reward_type('complex')

        # 生成对比分析
        self._generate_comparison_analysis(simplified_results, complex_results)

        # 保存模型
        self._save_trained_models(simplified_controller, complex_controller)

        print("\n🎉 完整对比实验完成!")
        print(f"📁 所有结果保存在: {self.output_dir}")

        return simplified_results, complex_results

    def _generate_comparison_analysis(self, simplified_results, complex_results):
        """生成对比分析图表和报告"""
        print("\n📊 生成对比分析...")

        # 创建对比图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('固定场景训练 - 简化 vs 复杂奖励函数对比', fontsize=16)

        stages = list(simplified_results['stages'].keys())

        # 1. 各阶段成功率对比
        simplified_success = [simplified_results['stages'][stage]['success_count'] /
                             simplified_results['stages'][stage]['episodes'] for stage in stages]
        complex_success = [complex_results['stages'][stage]['success_count'] /
                          complex_results['stages'][stage]['episodes'] for stage in stages]

        x = np.arange(len(stages))
        width = 0.35

        axes[0, 0].bar(x - width/2, simplified_success, width, label='简化奖励', color='lightblue')
        axes[0, 0].bar(x + width/2, complex_success, width, label='复杂奖励', color='lightcoral')
        axes[0, 0].set_title('各阶段成功率对比')
        axes[0, 0].set_ylabel('成功率')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(stages)
        axes[0, 0].legend()
        axes[0, 0].set_ylim(0, 1)

        # 添加数值标签
        for i, (s_rate, c_rate) in enumerate(zip(simplified_success, complex_success)):
            axes[0, 0].text(i - width/2, s_rate + 0.02, f'{s_rate:.1%}', ha='center', fontsize=9)
            axes[0, 0].text(i + width/2, c_rate + 0.02, f'{c_rate:.1%}', ha='center', fontsize=9)

        # 2. 平均奖励对比
        simplified_rewards = [np.mean(simplified_results['stages'][stage]['episode_rewards']) for stage in stages]
        complex_rewards = [np.mean(complex_results['stages'][stage]['episode_rewards']) for stage in stages]

        axes[0, 1].bar(x - width/2, simplified_rewards, width, label='简化奖励', color='lightblue')
        axes[0, 1].bar(x + width/2, complex_rewards, width, label='复杂奖励', color='lightcoral')
        axes[0, 1].set_title('各阶段平均奖励对比')
        axes[0, 1].set_ylabel('平均奖励')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels(stages)
        axes[0, 1].legend()

        # 3. 训练时间对比
        simplified_times = [simplified_results['stages'][stage]['training_time'] for stage in stages]
        complex_times = [complex_results['stages'][stage]['training_time'] for stage in stages]

        axes[0, 2].bar(x - width/2, simplified_times, width, label='简化奖励', color='lightblue')
        axes[0, 2].bar(x + width/2, complex_times, width, label='复杂奖励', color='lightcoral')
        axes[0, 2].set_title('各阶段训练时间对比')
        axes[0, 2].set_ylabel('训练时间 (秒)')
        axes[0, 2].set_xticks(x)
        axes[0, 2].set_xticklabels(stages)
        axes[0, 2].legend()

        # 4. 奖励曲线对比（所有阶段连续）
        simplified_all_rewards = []
        complex_all_rewards = []
        stage_boundaries = [0]

        for stage in stages:
            simplified_all_rewards.extend(simplified_results['stages'][stage]['episode_rewards'])
            complex_all_rewards.extend(complex_results['stages'][stage]['episode_rewards'])
            stage_boundaries.append(len(simplified_all_rewards))

        episodes = range(len(simplified_all_rewards))
        axes[1, 0].plot(episodes, simplified_all_rewards, label='简化奖励', color='blue', alpha=0.7)
        axes[1, 0].plot(episodes, complex_all_rewards, label='复杂奖励', color='red', alpha=0.7)

        # 添加阶段分界线
        for boundary in stage_boundaries[1:-1]:
            axes[1, 0].axvline(x=boundary, color='gray', linestyle='--', alpha=0.5)

        axes[1, 0].set_title('完整训练奖励曲线对比')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('奖励')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 5. 总体性能指标对比
        metrics = ['总体成功率', '平均训练时间', '总训练时间']
        simplified_metrics = [
            simplified_results['overall_success_rate'],
            np.mean(simplified_times),
            simplified_results['total_training_time']
        ]
        complex_metrics = [
            complex_results['overall_success_rate'],
            np.mean(complex_times),
            complex_results['total_training_time']
        ]

        # 归一化指标用于雷达图显示
        normalized_simplified = [
            simplified_metrics[0],  # 成功率保持原值
            1 - simplified_metrics[1] / max(simplified_metrics[1], complex_metrics[1]),  # 时间越短越好
            1 - simplified_metrics[2] / max(simplified_metrics[2], complex_metrics[2])   # 时间越短越好
        ]
        normalized_complex = [
            complex_metrics[0],
            1 - complex_metrics[1] / max(simplified_metrics[1], complex_metrics[1]),
            1 - complex_metrics[2] / max(simplified_metrics[2], complex_metrics[2])
        ]

        x_metrics = np.arange(len(metrics))
        axes[1, 1].bar(x_metrics - width/2, normalized_simplified, width, label='简化奖励', color='lightblue')
        axes[1, 1].bar(x_metrics + width/2, normalized_complex, width, label='复杂奖励', color='lightcoral')
        axes[1, 1].set_title('总体性能指标对比（归一化）')
        axes[1, 1].set_ylabel('性能得分')
        axes[1, 1].set_xticks(x_metrics)
        axes[1, 1].set_xticklabels(metrics, rotation=45)
        axes[1, 1].legend()
        axes[1, 1].set_ylim(0, 1)

        # 6. 学习曲线平滑对比
        def smooth_curve(data, window=50):
            return np.convolve(data, np.ones(window)/window, mode='valid')

        if len(simplified_all_rewards) > 50:
            smooth_simplified = smooth_curve(simplified_all_rewards)
            smooth_complex = smooth_curve(complex_all_rewards)
            smooth_episodes = range(len(smooth_simplified))

            axes[1, 2].plot(smooth_episodes, smooth_simplified, label='简化奖励(平滑)', color='blue', linewidth=2)
            axes[1, 2].plot(smooth_episodes, smooth_complex, label='复杂奖励(平滑)', color='red', linewidth=2)
            axes[1, 2].set_title('学习曲线对比（50-episode移动平均）')
            axes[1, 2].set_xlabel('Episode')
            axes[1, 2].set_ylabel('平滑奖励')
            axes[1, 2].legend()
            axes[1, 2].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存对比图表
        comparison_plot = f"{self.output_dir}/reward_comparison_analysis.png"
        plt.savefig(comparison_plot, dpi=300, bbox_inches='tight')
        print(f"📈 对比分析图表已保存: {comparison_plot}")

        plt.show()

        # 生成文字报告
        self._generate_comparison_report(simplified_results, complex_results)

    def _generate_comparison_report(self, simplified_results, complex_results):
        """生成对比分析报告"""
        report_filename = f"{self.output_dir}/comparison_report.md"

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("# 固定场景训练 - 奖励函数对比报告\n\n")
            f.write(f"**实验时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**随机种子**: {self.seed}\n")
            f.write(f"**训练配置**: 固定场景分阶段训练\n\n")

            f.write("## 🎯 实验设计\n\n")
            f.write("本实验采用固定场景配置，确保两种奖励函数在完全相同的环境下训练：\n\n")
            f.write("- **阶段1**: 5个固定静态障碍物（简单场景）- 500 episodes\n")
            f.write("- **阶段2**: 15个固定静态障碍物（继承阶段1 + 新增10个）- 1000 episodes\n")
            f.write("- **阶段3**: 15个静态 + 3个固定动态障碍物 - 500 episodes\n\n")

            f.write("## 📊 总体结果对比\n\n")
            f.write("| 指标 | 简化奖励函数 | 复杂奖励函数 | 优势 |\n")
            f.write("|------|-------------|-------------|------|\n")
            f.write(f"| 总体成功率 | {simplified_results['overall_success_rate']:.1%} | {complex_results['overall_success_rate']:.1%} | ")
            if simplified_results['overall_success_rate'] > complex_results['overall_success_rate']:
                f.write("简化奖励 ✅ |\n")
            else:
                f.write("复杂奖励 ✅ |\n")

            f.write(f"| 总训练时间 | {simplified_results['total_training_time']:.1f}s | {complex_results['total_training_time']:.1f}s | ")
            if simplified_results['total_training_time'] < complex_results['total_training_time']:
                f.write("简化奖励 ✅ |\n")
            else:
                f.write("复杂奖励 ✅ |\n")

            f.write("\n## 📈 各阶段详细对比\n\n")

            stages = list(simplified_results['stages'].keys())
            for stage in stages:
                s_stage = simplified_results['stages'][stage]
                c_stage = complex_results['stages'][stage]

                f.write(f"### {stage.upper()}\n\n")
                f.write("| 指标 | 简化奖励 | 复杂奖励 | 差异 |\n")
                f.write("|------|----------|----------|------|\n")

                s_success = s_stage['success_count'] / s_stage['episodes']
                c_success = c_stage['success_count'] / c_stage['episodes']
                f.write(f"| 成功率 | {s_success:.1%} | {c_success:.1%} | {s_success - c_success:+.1%} |\n")

                s_reward = np.mean(s_stage['episode_rewards'])
                c_reward = np.mean(c_stage['episode_rewards'])
                f.write(f"| 平均奖励 | {s_reward:.2f} | {c_reward:.2f} | {s_reward - c_reward:+.2f} |\n")

                f.write(f"| 训练时间 | {s_stage['training_time']:.1f}s | {c_stage['training_time']:.1f}s | {s_stage['training_time'] - c_stage['training_time']:+.1f}s |\n")
                f.write("\n")

            f.write("## 🔍 关键发现\n\n")

            # 分析关键发现
            if simplified_results['overall_success_rate'] > complex_results['overall_success_rate']:
                f.write("1. **简化奖励函数表现更优**: 在固定场景下，简化奖励函数实现了更高的成功率\n")
            else:
                f.write("1. **复杂奖励函数表现更优**: 在固定场景下，复杂奖励函数实现了更高的成功率\n")

            f.write("2. **固定场景的优势**: 通过使用固定场景，消除了环境随机性对训练结果的影响\n")
            f.write("3. **分阶段训练效果**: 从简单到复杂的渐进式训练体现了持续学习的优势\n")

            f.write("\n## 💡 结论与建议\n\n")
            f.write("基于固定场景的对比实验结果，我们可以更客观地评估不同奖励函数的性能。\n")
            f.write("这种方法消除了环境随机性的干扰，使得对比结果更加可信和可重现。\n")

        print(f"📝 对比报告已保存: {report_filename}")

    def _save_trained_models(self, simplified_controller, complex_controller):
        """保存训练好的模型"""
        # 保存简化奖励函数模型
        simplified_model_path = f"{self.output_dir}/simplified_reward_model.pth"
        simplified_controller.save_model(simplified_model_path)

        # 保存复杂奖励函数模型
        complex_model_path = f"{self.output_dir}/complex_reward_model.pth"
        complex_controller.save_model(complex_model_path)

        print(f"🤖 模型已保存:")
        print(f"  简化奖励模型: {simplified_model_path}")
        print(f"  复杂奖励模型: {complex_model_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='固定场景分阶段训练系统')
    parser.add_argument('--reward-type', choices=['simplified', 'complex', 'both'],
                       default='both', help='训练的奖励函数类型')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    args = parser.parse_args()

    # 创建训练器
    trainer = FixedScenarioTraining(seed=args.seed)

    if args.reward_type == 'both':
        # 运行完整对比实验
        trainer.run_complete_comparison()
    else:
        # 只训练指定的奖励函数类型
        trainer.train_single_reward_type(args.reward_type)

if __name__ == "__main__":
    main()
