"""
简化奖励函数演示脚本
展示701_2简化奖励函数在主文件夹分阶段训练环境中的应用效果
"""

import numpy as np
import matplotlib.pyplot as plt
from dwa_rl_core import StabilizedEnvironment
from train_dwa_rl import train_dwa_rl_model

def demonstrate_reward_differences():
    """演示两种奖励函数的差异"""
    print("🎯 奖励函数对比演示")
    print("=" * 50)
    
    # 创建两种环境
    env_simplified = StabilizedEnvironment(reward_type='simplified')
    env_complex = StabilizedEnvironment(reward_type='complex')
    
    print("📊 不同场景下的奖励对比:")
    print("-" * 50)
    
    scenarios = [
        ("正常移动", np.array([1.0, 1.0, 1.0])),
        ("快速移动", np.array([3.0, 3.0, 3.0])),
        ("缓慢移动", np.array([0.5, 0.5, 0.5])),
        ("偏离目标", np.array([-1.0, 1.0, 0.0])),
    ]
    
    for scenario_name, action in scenarios:
        # 重置环境
        env_simplified.reset()
        env_complex.reset()
        
        # 执行相同动作
        _, reward_simple, _, _ = env_simplified.step(action)
        _, reward_complex, _, _ = env_complex.step(action)
        
        print(f"{scenario_name:12s}: 简化={reward_simple:7.3f}, 复杂={reward_complex:7.3f}, 差异={reward_simple-reward_complex:7.3f}")
    
    print("\n✨ 简化奖励函数特点:")
    print("  • 强烈终止信号: 成功+100, 失败-100")
    print("  • 距离主导: -goal_dist/50.0 (清晰的梯度)")
    print("  • 最小约束: 仅危险时惩罚")
    print("  • 效率激励: 每步-0.1")
    
    print("\n🔧 复杂奖励函数特点:")
    print("  • 多项平衡: 6个小分量组合")
    print("  • 权重调节: 需要手工平衡")
    print("  • 信号微弱: 总变化范围有限")
    print("  • 目标冲突: 速度vs安全性等")

def quick_training_demo():
    """快速训练演示"""
    print("\n🚀 快速训练演示 (20 episodes)")
    print("=" * 50)
    
    print("训练简化奖励函数模型...")
    episode_rewards_simple, _, _, _ = train_dwa_rl_model(
        num_episodes=20,
        enable_visualization=False,
        save_outputs=False,
        environment_config='simple',
        reward_type='simplified'
    )
    
    print("\n训练复杂奖励函数模型...")
    episode_rewards_complex, _, _, _ = train_dwa_rl_model(
        num_episodes=20,
        enable_visualization=False,
        save_outputs=False,
        environment_config='simple',
        reward_type='complex'
    )
    
    # 分析结果
    print("\n📊 训练结果对比:")
    print("-" * 50)
    
    simple_stats = {
        'mean': np.mean(episode_rewards_simple),
        'std': np.std(episode_rewards_simple),
        'final_5': np.mean(episode_rewards_simple[-5:]),
        'improvement': np.mean(episode_rewards_simple[-5:]) - np.mean(episode_rewards_simple[:5])
    }
    
    complex_stats = {
        'mean': np.mean(episode_rewards_complex),
        'std': np.std(episode_rewards_complex),
        'final_5': np.mean(episode_rewards_complex[-5:]),
        'improvement': np.mean(episode_rewards_complex[-5:]) - np.mean(episode_rewards_complex[:5])
    }
    
    print(f"简化奖励函数:")
    print(f"  平均奖励: {simple_stats['mean']:8.2f}")
    print(f"  奖励标准差: {simple_stats['std']:6.2f}")
    print(f"  最后5次平均: {simple_stats['final_5']:6.2f}")
    print(f"  学习改善: {simple_stats['improvement']:8.2f}")
    
    print(f"\n复杂奖励函数:")
    print(f"  平均奖励: {complex_stats['mean']:8.2f}")
    print(f"  奖励标准差: {complex_stats['std']:6.2f}")
    print(f"  最后5次平均: {complex_stats['final_5']:6.2f}")
    print(f"  学习改善: {complex_stats['improvement']:8.2f}")
    
    print(f"\n📈 对比分析:")
    print(f"  奖励稳定性改善: {complex_stats['std'] - simple_stats['std']:+.2f} (负值更好)")
    print(f"  学习效果改善: {simple_stats['improvement'] - complex_stats['improvement']:+.2f}")
    print(f"  最终性能差异: {simple_stats['final_5'] - complex_stats['final_5']:+.2f}")
    
    return episode_rewards_simple, episode_rewards_complex

def analyze_reward_characteristics():
    """分析奖励函数特性"""
    print("\n🔍 奖励函数特性分析")
    print("=" * 50)
    
    env_simplified = StabilizedEnvironment(reward_type='simplified')
    env_complex = StabilizedEnvironment(reward_type='complex')
    
    # 模拟一个episode的奖励变化
    simplified_rewards = []
    complex_rewards = []
    distances = []
    
    env_simplified.reset()
    env_complex.reset()
    
    # 模拟向目标移动的过程
    for step in range(50):
        # 计算当前距离
        dist = np.linalg.norm(env_simplified.state[:3] - env_simplified.goal)
        distances.append(dist)
        
        # 向目标移动的动作
        direction = (env_simplified.goal - env_simplified.state[:3])
        direction = direction / np.linalg.norm(direction) * 2.0  # 标准化并设置速度
        
        # 执行动作
        _, reward_simple, done_simple, _ = env_simplified.step(direction)
        _, reward_complex, done_complex, _ = env_complex.step(direction)
        
        simplified_rewards.append(reward_simple)
        complex_rewards.append(reward_complex)
        
        if done_simple or done_complex:
            break
    
    print(f"模拟轨迹分析 ({len(simplified_rewards)} 步):")
    print(f"  初始距离: {distances[0]:.1f}")
    print(f"  最终距离: {distances[-1]:.1f}")
    print(f"  距离减少: {distances[0] - distances[-1]:.1f}")
    
    print(f"\n奖励变化特性:")
    print(f"  简化奖励范围: [{min(simplified_rewards):.2f}, {max(simplified_rewards):.2f}]")
    print(f"  复杂奖励范围: [{min(complex_rewards):.2f}, {max(complex_rewards):.2f}]")
    print(f"  简化奖励变化: {max(simplified_rewards) - min(simplified_rewards):.2f}")
    print(f"  复杂奖励变化: {max(complex_rewards) - min(complex_rewards):.2f}")
    
    # 计算奖励与距离的相关性
    simple_correlation = np.corrcoef(distances, simplified_rewards)[0, 1]
    complex_correlation = np.corrcoef(distances, complex_rewards)[0, 1]
    
    print(f"\n与距离的相关性:")
    print(f"  简化奖励: {simple_correlation:.3f}")
    print(f"  复杂奖励: {complex_correlation:.3f}")
    print(f"  (负相关性越强，表示越能引导向目标移动)")

def main():
    """主演示函数"""
    print("🎯 701_2简化奖励函数在主文件夹分阶段训练中的应用演示")
    print("=" * 80)
    
    # 1. 演示奖励函数差异
    demonstrate_reward_differences()
    
    # 2. 分析奖励函数特性
    analyze_reward_characteristics()
    
    # 3. 快速训练演示
    episode_rewards_simple, episode_rewards_complex = quick_training_demo()
    
    print("\n🎉 演示完成!")
    print("\n📋 总结:")
    print("✅ 简化奖励函数已成功集成到主文件夹的分阶段训练环境中")
    print("✅ 三项简洁设计: 强烈终止信号 + 距离主导 + 最小安全约束")
    print("✅ 相比复杂奖励函数，简化版本提供更清晰的学习信号")
    print("✅ 可以在分阶段训练中使用 reward_type='simplified' 参数")
    
    print(f"\n🚀 使用方法:")
    print(f"# 分阶段训练使用简化奖励函数")
    print(f"python staged_training.py")
    print(f"")
    print(f"# 单独训练使用简化奖励函数")
    print(f"python train_dwa_rl.py --reward-type simplified")
    print(f"")
    print(f"# 对比训练两种奖励函数")
    print(f"python staged_reward_comparison.py")

if __name__ == "__main__":
    main()
