"""
快速测试修复后的固定场景训练
"""

from fixed_scenario_training import FixedScenarioTraining

def test_quick_training():
    """快速测试训练"""
    print("🧪 测试修复后的固定场景训练...")
    
    # 创建训练器
    trainer = FixedScenarioTraining(seed=42)
    
    # 修改为极少的episodes进行快速测试
    trainer.training_stages = {
        'stage1': {'episodes': 3, 'scenario': 'stage1', 'description': '阶段1：简单场景训练'},
    }
    
    print("🚀 开始快速测试（只训练3个episodes）...")
    
    try:
        # 测试简化奖励函数
        results, controller = trainer.train_single_reward_type('simplified')
        print("✅ 训练成功!")
        print(f"📊 总体成功率: {results['overall_success_rate']:.1%}")
        print(f"⏱️ 训练时间: {results['total_training_time']:.1f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_quick_training()
    if success:
        print("\n🎉 修复成功! 现在可以运行完整的4000次训练:")
        print("python fixed_scenario_training.py --reward-type both")
    else:
        print("\n❌ 仍有问题，需要进一步调试")
