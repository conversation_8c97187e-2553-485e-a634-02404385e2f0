"""
演示固定场景的概念
不依赖复杂的TD3模块，直接展示固定场景的优势
"""

import numpy as np
import matplotlib.pyplot as plt
from fixed_scenario_config import get_fixed_scenario_config
from simple_environment import SimpleUAVEnvironment

class SimpleFixedScenarioDemo:
    """简单的固定场景演示"""
    
    def __init__(self):
        print("🎯 固定场景训练演示")
        print("=" * 60)
    
    def demonstrate_scenario_consistency(self):
        """演示场景一致性"""
        print("\n📍 演示1: 场景一致性")
        print("-" * 30)
        
        # 获取阶段1的固定场景配置
        scenario_config = get_fixed_scenario_config('stage1')
        print(f"使用场景: {scenario_config['description']}")
        
        # 创建环境
        env = SimpleUAVEnvironment(fixed_scenario_config=scenario_config)
        
        print("\n多次重置环境，验证障碍物配置是否一致:")
        
        for reset_num in range(3):
            state = env.reset()
            print(f"  重置 {reset_num + 1}: 障碍物数量 = {len(env.obstacles)}")
            
            # 打印前3个障碍物的位置和半径
            for i, obs in enumerate(env.obstacles[:3]):
                center = obs['center']
                radius = obs['radius']
                print(f"    障碍物{i+1}: 位置=({center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}), 半径={radius:.1f}")
        
        print("✅ 可以看到每次重置后障碍物配置完全一致!")
    
    def demonstrate_random_vs_fixed(self):
        """演示随机场景 vs 固定场景的区别"""
        print("\n📍 演示2: 随机场景 vs 固定场景")
        print("-" * 30)
        
        # 创建随机场景环境
        env_random = SimpleUAVEnvironment()  # 不传入fixed_scenario_config
        
        # 创建固定场景环境
        scenario_config = get_fixed_scenario_config('stage1')
        env_fixed = SimpleUAVEnvironment(fixed_scenario_config=scenario_config)
        
        print("随机场景环境 - 多次重置:")
        for i in range(3):
            env_random.reset()
            print(f"  重置 {i+1}: 障碍物数量 = {len(env_random.obstacles)}")
            if len(env_random.obstacles) > 0:
                first_obs = env_random.obstacles[0]
                print(f"    第1个障碍物: 位置=({first_obs['center'][0]:.1f}, {first_obs['center'][1]:.1f}, {first_obs['center'][2]:.1f})")
        
        print("\n固定场景环境 - 多次重置:")
        for i in range(3):
            env_fixed.reset()
            print(f"  重置 {i+1}: 障碍物数量 = {len(env_fixed.obstacles)}")
            if len(env_fixed.obstacles) > 0:
                first_obs = env_fixed.obstacles[0]
                print(f"    第1个障碍物: 位置=({first_obs['center'][0]:.1f}, {first_obs['center'][1]:.1f}, {first_obs['center'][2]:.1f})")
        
        print("\n💡 观察: 随机场景每次都不同，固定场景每次都相同!")
    
    def demonstrate_progressive_stages(self):
        """演示渐进式阶段设计"""
        print("\n📍 演示3: 渐进式阶段设计")
        print("-" * 30)
        
        stages = ['stage1', 'stage2', 'stage3']
        
        for stage in stages:
            scenario_config = get_fixed_scenario_config(stage)
            env = SimpleUAVEnvironment(fixed_scenario_config=scenario_config)
            env.reset()
            
            print(f"\n{scenario_config['description']}:")
            print(f"  静态障碍物: {len(env.obstacles)}个")
            print(f"  动态障碍物: {len(env.dynamic_obstacles)}个")
            print(f"  起点: ({env.start[0]:.1f}, {env.start[1]:.1f}, {env.start[2]:.1f})")
            print(f"  终点: ({env.goal[0]:.1f}, {env.goal[1]:.1f}, {env.goal[2]:.1f})")
        
        print("\n💡 观察: 从简单到复杂的渐进式设计，体现了课程学习的思想!")
    
    def simulate_reward_comparison(self):
        """模拟奖励函数对比"""
        print("\n📍 演示4: 奖励函数对比的公平性")
        print("-" * 30)
        
        # 使用相同的固定场景
        scenario_config = get_fixed_scenario_config('stage1')
        
        # 模拟两种不同的奖励函数在相同场景下的表现
        print("在相同的固定场景下测试两种奖励函数:")
        
        # 创建环境
        env = SimpleUAVEnvironment(fixed_scenario_config=scenario_config)
        
        # 模拟几个episode的训练
        for episode in range(3):
            state = env.reset()
            
            # 模拟简化奖励函数的表现
            simple_reward = self._simulate_simple_reward_episode(env)
            
            # 重置环境（相同场景）
            env.reset()
            
            # 模拟复杂奖励函数的表现
            complex_reward = self._simulate_complex_reward_episode(env)
            
            print(f"  Episode {episode+1}:")
            print(f"    简化奖励函数总奖励: {simple_reward:.2f}")
            print(f"    复杂奖励函数总奖励: {complex_reward:.2f}")
        
        print("\n💡 观察: 两种奖励函数在完全相同的场景下进行对比，结果更加可信!")
    
    def _simulate_simple_reward_episode(self, env):
        """模拟简化奖励函数的episode"""
        total_reward = 0
        for step in range(10):  # 模拟10步
            # 简单的随机动作
            action = np.random.uniform(-1, 1, 3)
            
            # 模拟简化奖励计算（距离目标越近奖励越高）
            distance_to_goal = np.linalg.norm(env.state[:3] - env.goal)
            reward = -distance_to_goal * 0.1  # 简化的奖励函数
            
            total_reward += reward
            
            # 简单的状态更新
            env.state[:3] += action * 0.5
        
        return total_reward
    
    def _simulate_complex_reward_episode(self, env):
        """模拟复杂奖励函数的episode"""
        total_reward = 0
        for step in range(10):  # 模拟10步
            # 简单的随机动作
            action = np.random.uniform(-1, 1, 3)
            
            # 模拟复杂奖励计算（包含多个因素）
            distance_to_goal = np.linalg.norm(env.state[:3] - env.goal)
            
            # 计算到最近障碍物的距离
            min_obstacle_dist = float('inf')
            for obs in env.obstacles:
                dist = np.linalg.norm(env.state[:3] - obs['center']) - obs['radius']
                min_obstacle_dist = min(min_obstacle_dist, dist)
            
            # 复杂奖励函数（多个组件）
            goal_reward = -distance_to_goal * 0.1
            safety_reward = min(min_obstacle_dist * 0.05, 1.0)
            action_penalty = -np.linalg.norm(action) * 0.01
            
            reward = goal_reward + safety_reward + action_penalty
            total_reward += reward
            
            # 简单的状态更新
            env.state[:3] += action * 0.5
        
        return total_reward
    
    def run_all_demonstrations(self):
        """运行所有演示"""
        self.demonstrate_scenario_consistency()
        self.demonstrate_random_vs_fixed()
        self.demonstrate_progressive_stages()
        self.simulate_reward_comparison()
        
        print("\n" + "=" * 60)
        print("🎉 固定场景训练演示完成!")
        print("\n💡 关键优势:")
        print("  1. 场景一致性 - 每个episode使用相同的障碍物配置")
        print("  2. 公平对比 - 不同奖励函数在相同环境下训练")
        print("  3. 渐进学习 - 从简单到复杂的阶段性训练")
        print("  4. 可重现性 - 固定种子确保实验可重复")
        print("\n🚀 现在可以运行真正的固定场景训练:")
        print("   python fixed_scenario_training.py --reward-type both")

def main():
    """主函数"""
    demo = SimpleFixedScenarioDemo()
    demo.run_all_demonstrations()

if __name__ == "__main__":
    main()
