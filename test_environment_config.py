"""
测试环境配置是否正确工作
验证不同环境配置产生不同数量的障碍物
"""

from dwa_rl_core import StabilizedEnvironment
from environment_config import ENVIRONMENT_CONFIGS

def test_environment_configs():
    """测试不同环境配置"""
    print("🧪 测试环境配置差异")
    print("=" * 50)
    
    configs_to_test = ['simple', 'complex_static', 'complex_dynamic']
    
    for config_name in configs_to_test:
        print(f"\n🎯 测试环境配置: {config_name}")
        print("-" * 30)
        
        config = ENVIRONMENT_CONFIGS[config_name]
        print(f"配置描述: {config['description']}")
        print(f"静态障碍物数量范围: {config['static_obstacle_count']}")
        print(f"启用动态障碍物: {config['enable_dynamic_obstacles']}")
        
        # 创建环境
        env = StabilizedEnvironment(
            enable_dynamic_obstacles=config['enable_dynamic_obstacles'],
            reward_type='simplified',
            environment_config=config
        )
        
        print(f"✅ 实际生成静态障碍物: {len(env.obstacles)} 个")
        if config['enable_dynamic_obstacles']:
            print(f"✅ 实际生成动态障碍物: {len(env.dynamic_obstacles)} 个")
        
        # 重置几次看看随机性
        obstacle_counts = []
        for i in range(3):
            env.reset()
            obstacle_counts.append(len(env.obstacles))
        
        print(f"📊 3次重置的障碍物数量: {obstacle_counts}")
        print(f"📊 平均障碍物数量: {sum(obstacle_counts)/len(obstacle_counts):.1f}")

def main():
    test_environment_configs()
    
    print(f"\n🎉 测试完成!")
    print(f"现在不同环境配置应该产生不同数量的障碍物了。")

if __name__ == "__main__":
    main()
