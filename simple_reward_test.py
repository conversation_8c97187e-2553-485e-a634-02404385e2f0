"""
简单的奖励函数测试
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.getcwd())

def test_basic_import():
    """测试基本导入"""
    try:
        print("测试导入...")
        from simple_environment import SimpleUAVEnvironment
        print("✅ SimpleUAVEnvironment 导入成功")
        
        # 创建基础环境
        env = SimpleUAVEnvironment()
        print("✅ 基础环境创建成功")
        
        # 测试重置
        state = env.reset()
        print(f"✅ 环境重置成功，状态维度: {len(state)}")
        
        return True
    except Exception as e:
        print(f"❌ 基础导入测试失败: {e}")
        return False

def test_stabilized_environment():
    """测试稳定化环境"""
    try:
        print("\n测试稳定化环境...")
        from dwa_rl_core import StabilizedEnvironment
        print("✅ StabilizedEnvironment 导入成功")
        
        # 创建简化奖励环境
        env_simple = StabilizedEnvironment(reward_type='simplified')
        print("✅ 简化奖励环境创建成功")
        
        # 创建复杂奖励环境
        env_complex = StabilizedEnvironment(reward_type='complex')
        print("✅ 复杂奖励环境创建成功")
        
        # 测试重置
        state_simple = env_simple.reset()
        state_complex = env_complex.reset()
        print(f"✅ 环境重置成功")
        
        # 测试奖励计算
        action = np.array([1.0, 1.0, 1.0])
        
        _, reward_simple, _, _ = env_simple.step(action)
        _, reward_complex, _, _ = env_complex.step(action)
        
        print(f"✅ 奖励计算成功:")
        print(f"  简化奖励: {reward_simple:.3f}")
        print(f"  复杂奖励: {reward_complex:.3f}")
        
        return True
    except Exception as e:
        print(f"❌ 稳定化环境测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧪 简单奖励函数测试")
    print("=" * 40)
    
    success1 = test_basic_import()
    success2 = test_stabilized_environment()
    
    print(f"\n📊 测试结果:")
    print(f"基础导入: {'✅' if success1 else '❌'}")
    print(f"稳定化环境: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过!")
    else:
        print("\n⚠️ 部分测试失败")

if __name__ == "__main__":
    main()
