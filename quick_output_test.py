"""
快速测试改进后的输出
"""

import numpy as np
from fixed_scenario_config import get_fixed_scenario_config
from dwa_rl_core import StabilizedEnvironment

def test_reset_output():
    """测试重置输出"""
    print("🧪 测试环境重置输出...")
    
    # 获取场景配置
    scenario_config = get_fixed_scenario_config('stage1')
    
    # 创建环境
    env = StabilizedEnvironment(
        reward_type='simplified',
        fixed_scenario_config=scenario_config
    )
    
    print("\n第一次重置（verbose=True）:")
    state1 = env.reset(verbose=True)
    
    print("\n第二次重置（verbose=False）:")
    state2 = env.reset(verbose=False)
    
    print("\n第三次重置（verbose=False）:")
    state3 = env.reset(verbose=False)
    
    print("\n✅ 重置输出测试完成!")
    print("可以看到只有第一次重置显示了详细的场景信息")

if __name__ == "__main__":
    test_reset_output()
