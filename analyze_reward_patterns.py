"""
分析奖励模式，理解简化vs复杂奖励函数的差异
"""

import numpy as np
from dwa_rl_core import StabilizedEnvironment
from environment_config import ENVIRONMENT_CONFIGS

def analyze_reward_patterns():
    """分析两种奖励函数的奖励模式"""
    print("🔍 分析奖励函数模式")
    print("=" * 60)
    
    # 创建两种环境
    simple_config = ENVIRONMENT_CONFIGS['simple']
    env_simplified = StabilizedEnvironment(
        enable_dynamic_obstacles=False,
        reward_type='simplified',
        environment_config=simple_config
    )
    
    env_complex = StabilizedEnvironment(
        enable_dynamic_obstacles=False,
        reward_type='complex',
        environment_config=simple_config
    )
    
    print("\n📊 奖励函数特性分析")
    print("-" * 40)
    
    # 模拟不同场景的奖励
    scenarios = [
        ("正常移动", np.array([1.0, 1.0, 1.0])),
        ("快速移动", np.array([2.0, 2.0, 2.0])),
        ("接近目标", None),  # 特殊处理
        ("到达目标", None),  # 特殊处理
        ("接近障碍物", None),  # 特殊处理
    ]
    
    simplified_rewards = []
    complex_rewards = []
    
    for scenario_name, action in scenarios:
        print(f"\n🎯 场景: {scenario_name}")
        
        # 重置环境
        env_simplified.reset()
        env_complex.reset()
        
        if scenario_name == "接近目标":
            # 设置位置接近目标
            env_simplified.state[:3] = env_simplified.goal - np.array([10.0, 10.0, 10.0])
            env_complex.state[:3] = env_complex.goal - np.array([10.0, 10.0, 10.0])
            action = np.array([1.0, 1.0, 1.0])
        elif scenario_name == "到达目标":
            # 设置位置非常接近目标
            env_simplified.state[:3] = env_simplified.goal - np.array([2.0, 2.0, 2.0])
            env_complex.state[:3] = env_complex.goal - np.array([2.0, 2.0, 2.0])
            action = np.array([2.0, 2.0, 2.0])
        elif scenario_name == "接近障碍物":
            # 设置位置接近第一个障碍物
            if env_simplified.obstacles:
                obs = env_simplified.obstacles[0]
                danger_pos = obs['center'] - np.array([obs['radius'] + 2.0, 0, 0])
                env_simplified.state[:3] = danger_pos
                env_complex.state[:3] = danger_pos
                action = np.array([1.0, 0.0, 0.0])
        
        # 执行动作并获取奖励
        _, reward_simple, done_simple, info_simple = env_simplified.step(action)
        _, reward_complex, done_complex, info_complex = env_complex.step(action)
        
        simplified_rewards.append(reward_simple)
        complex_rewards.append(reward_complex)
        
        print(f"  简化奖励: {reward_simple:8.3f}, 完成: {done_simple}")
        print(f"  复杂奖励: {reward_complex:8.3f}, 完成: {done_complex}")
        
        if info_simple:
            print(f"  简化信息: {info_simple}")
        if info_complex:
            print(f"  复杂信息: {info_complex}")
    
    print(f"\n📈 奖励范围分析")
    print("-" * 40)
    print(f"简化奖励函数:")
    print(f"  范围: [{min(simplified_rewards):.3f}, {max(simplified_rewards):.3f}]")
    print(f"  平均: {np.mean(simplified_rewards):.3f}")
    print(f"  标准差: {np.std(simplified_rewards):.3f}")
    
    print(f"\n复杂奖励函数:")
    print(f"  范围: [{min(complex_rewards):.3f}, {max(complex_rewards):.3f}]")
    print(f"  平均: {np.mean(complex_rewards):.3f}")
    print(f"  标准差: {np.std(complex_rewards):.3f}")
    
    # 分析成功标准
    print(f"\n🎯 成功标准分析")
    print("-" * 40)
    
    success_rewards_simplified = [r for r in simplified_rewards if r > 50]
    success_rewards_complex = [r for r in complex_rewards if r > 50]
    
    print(f"使用阈值 > 50 的成功判断:")
    print(f"  简化奖励函数成功次数: {len(success_rewards_simplified)}/5")
    print(f"  复杂奖励函数成功次数: {len(success_rewards_complex)}/5")
    
    # 建议更好的成功标准
    print(f"\n💡 建议的成功标准:")
    print(f"  简化奖励函数: 奖励 > 50 (到达目标时获得+100)")
    print(f"  复杂奖励函数: 奖励 > 50 (多分量平衡的正奖励)")
    print(f"  或者更好的方法: 直接从环境的done信息中获取成功状态")

def test_episode_success_detection():
    """测试episode成功检测"""
    print(f"\n🧪 测试Episode成功检测")
    print("=" * 60)
    
    simple_config = ENVIRONMENT_CONFIGS['simple']
    env = StabilizedEnvironment(
        enable_dynamic_obstacles=False,
        reward_type='simplified',
        environment_config=simple_config
    )
    
    # 模拟一个成功的episode
    print("模拟成功到达目标的episode...")
    env.reset()
    
    # 直接设置位置到目标附近
    env.state[:3] = env.goal - np.array([3.0, 3.0, 3.0])
    
    # 向目标移动
    action = np.array([3.0, 3.0, 3.0])
    _, reward, done, info = env.step(action)
    
    print(f"奖励: {reward}")
    print(f"完成: {done}")
    print(f"信息: {info}")
    
    # 检查成功条件
    is_success = info.get('success', False) if info else False
    print(f"是否成功: {is_success}")
    
    if is_success:
        print("✅ 成功检测正常工作")
    else:
        print("❌ 成功检测可能有问题")

def main():
    analyze_reward_patterns()
    test_episode_success_detection()
    
    print(f"\n📋 总结:")
    print("1. 简化奖励函数主要产生负值奖励（距离主导）")
    print("2. 复杂奖励函数产生平衡的正值奖励")
    print("3. 使用固定阈值>50判断成功对简化奖励函数不公平")
    print("4. 应该使用环境返回的success信息来判断真正的成功")
    print("5. 或者为不同奖励函数使用不同的成功标准")

if __name__ == "__main__":
    main()
