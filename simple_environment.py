"""
简化的3D无人机环境
专门用于验证DWA-RL架构的基础版本

特点：
- 静止的球形障碍物（3-5个）
- 固定的目标点
- 简单的奖励函数
- 100x100x100的3D空间
"""

import numpy as np
import random

class SimpleUAVEnvironment:
    """简化的无人机3D环境"""
    def __init__(self, bounds=[100, 100, 100], enable_dynamic_obstacles=False, environment_config=None):
        self.bounds = bounds
        self.enable_dynamic_obstacles = enable_dynamic_obstacles
        self.environment_config = environment_config  # 环境配置
        self.dynamic_obstacles = []  # 动态障碍物列表
        self.reset()
        
    def reset(self):
        """重置环境"""
        # 固定起点和终点（便于调试）
        self.start = np.array([10.0, 10.0, 10.0], dtype=np.float64)
        self.goal = np.array([80.0, 80.0, 80.0], dtype=np.float64)
        
        # 生成更密集复杂的障碍物环境
        self.obstacles = []
        self._generate_complex_obstacles()
        
        # 初始状态 [x, y, z, vx, vy, vz]
        self.state = np.concatenate([self.start, [0.0, 0.0, 0.0]]).astype(np.float64)
        self.step_count = 0
        self.max_steps = 500
        
        return self._get_observation()

    def _generate_complex_obstacles(self):
        """生成更密集复杂的障碍物环境"""
        # 1. 核心障碍物群 - 在中心区域创建密集障碍物
        core_obstacles = [
            {'center': [35, 35, 35], 'radius': 6},
            {'center': [45, 25, 45], 'radius': 5},
            {'center': [25, 45, 25], 'radius': 5},
            {'center': [55, 35, 35], 'radius': 4},
            {'center': [35, 55, 45], 'radius': 4},
            {'center': [45, 45, 25], 'radius': 4},
        ]

        # 2. 通道障碍物 - 创建狭窄通道
        corridor_obstacles = [
            {'center': [20, 50, 50], 'radius': 3},
            {'center': [30, 50, 50], 'radius': 3},
            {'center': [50, 20, 60], 'radius': 3},
            {'center': [50, 30, 60], 'radius': 3},
            {'center': [60, 50, 20], 'radius': 3},
            {'center': [70, 50, 30], 'radius': 3},
        ]

        # 3. 边界障碍物 - 增加路径复杂性
        boundary_obstacles = [
            {'center': [15, 15, 50], 'radius': 4},
            {'center': [85, 15, 50], 'radius': 4},
            {'center': [15, 85, 50], 'radius': 4},
            {'center': [85, 85, 50], 'radius': 4},
            {'center': [50, 15, 15], 'radius': 3},
            {'center': [50, 85, 85], 'radius': 3},
        ]

        # 4. 随机小障碍物 - 增加环境不确定性
        num_random = random.randint(8, 12)
        random_obstacles = []
        for _ in range(num_random):
            # 避免在起点和终点附近生成障碍物
            while True:
                center = [
                    random.uniform(20, 80),
                    random.uniform(20, 80),
                    random.uniform(20, 80)
                ]
                # 确保不与起点终点太近
                start_dist = np.linalg.norm(np.array(center) - self.start)
                goal_dist = np.linalg.norm(np.array(center) - self.goal)
                if start_dist > 15 and goal_dist > 15:
                    break

            radius = random.uniform(2, 4)
            random_obstacles.append({'center': center, 'radius': radius})

        # 5. 组合所有障碍物
        all_obstacles = core_obstacles + corridor_obstacles + boundary_obstacles + random_obstacles

        # 6. 根据环境配置选择障碍物数量
        if self.environment_config and 'static_obstacle_count' in self.environment_config:
            # 使用配置中的障碍物数量范围
            min_count, max_count = self.environment_config['static_obstacle_count']
            num_selected = random.randint(min_count, max_count)
            print(f"🎯 环境配置: {self.environment_config.get('description', 'Unknown')}")
            print(f"📊 障碍物数量范围: {min_count}-{max_count}, 实际生成: {num_selected}")
        else:
            # 默认使用原来的数量
            num_selected = random.randint(15, 20)
            print(f"📊 使用默认障碍物数量: {num_selected}")

        selected_obstacles = random.sample(all_obstacles, min(num_selected, len(all_obstacles)))

        # 7. 转换为环境格式
        for obs_config in selected_obstacles:
            center = np.array(obs_config['center'], dtype=np.float64)
            radius = obs_config['radius']
            self.obstacles.append({'center': center, 'radius': radius})

        print(f"🚧 Generated {len(self.obstacles)} complex obstacles")

        # 8. 生成动态障碍物（如果启用）
        if self.enable_dynamic_obstacles:
            self._generate_dynamic_obstacles()

    def _generate_dynamic_obstacles(self):
        """生成动态障碍物"""
        num_dynamic = random.randint(2, 4)
        self.dynamic_obstacles = []

        for _ in range(num_dynamic):
            # 随机生成动态障碍物
            center = [
                random.uniform(30, 70),
                random.uniform(30, 70),
                random.uniform(30, 70)
            ]

            # 随机运动模式
            motion_type = random.choice(['linear', 'circular', 'oscillating'])

            if motion_type == 'linear':
                velocity = [
                    random.uniform(-2, 2),
                    random.uniform(-2, 2),
                    random.uniform(-2, 2)
                ]
                motion_params = {'velocity': velocity}
            elif motion_type == 'circular':
                center_orbit = center.copy()
                radius_orbit = random.uniform(5, 15)
                angular_speed = random.uniform(0.05, 0.15)
                motion_params = {
                    'center_orbit': center_orbit,
                    'radius_orbit': radius_orbit,
                    'angular_speed': angular_speed,
                    'phase': random.uniform(0, 2*np.pi)
                }
            else:  # oscillating
                amplitude = [
                    random.uniform(5, 15),
                    random.uniform(5, 15),
                    random.uniform(5, 15)
                ]
                frequency = [
                    random.uniform(0.02, 0.08),
                    random.uniform(0.02, 0.08),
                    random.uniform(0.02, 0.08)
                ]
                motion_params = {
                    'center_base': center.copy(),
                    'amplitude': amplitude,
                    'frequency': frequency,
                    'phase': [random.uniform(0, 2*np.pi) for _ in range(3)]
                }

            dynamic_obs = {
                'center': np.array(center, dtype=np.float64),
                'radius': random.uniform(3, 6),
                'motion_type': motion_type,
                'motion_params': motion_params,
                'time': 0.0
            }

            self.dynamic_obstacles.append(dynamic_obs)

        print(f"🔄 Generated {len(self.dynamic_obstacles)} dynamic obstacles")

    def _update_dynamic_obstacles(self):
        """更新动态障碍物位置"""
        if not self.enable_dynamic_obstacles:
            return

        dt = 0.1  # 时间步长

        for obs in self.dynamic_obstacles:
            obs['time'] += dt

            if obs['motion_type'] == 'linear':
                # 线性运动
                velocity = obs['motion_params']['velocity']
                obs['center'] += np.array(velocity) * dt

                # 边界反弹
                for i in range(3):
                    if obs['center'][i] <= obs['radius'] or obs['center'][i] >= self.bounds[i] - obs['radius']:
                        obs['motion_params']['velocity'][i] *= -1
                        obs['center'][i] = np.clip(obs['center'][i], obs['radius'], self.bounds[i] - obs['radius'])

            elif obs['motion_type'] == 'circular':
                # 圆周运动
                params = obs['motion_params']
                angle = params['angular_speed'] * obs['time'] + params['phase']
                obs['center'][0] = params['center_orbit'][0] + params['radius_orbit'] * np.cos(angle)
                obs['center'][1] = params['center_orbit'][1] + params['radius_orbit'] * np.sin(angle)
                # Z轴保持相对稳定，轻微振荡
                obs['center'][2] = params['center_orbit'][2] + 3 * np.sin(angle * 0.5)

            else:  # oscillating
                # 振荡运动
                params = obs['motion_params']
                for i in range(3):
                    offset = params['amplitude'][i] * np.sin(params['frequency'][i] * obs['time'] + params['phase'][i])
                    obs['center'][i] = params['center_base'][i] + offset

            # 确保动态障碍物在边界内
            obs['center'] = np.clip(obs['center'], obs['radius'], np.array(self.bounds) - obs['radius'])

    def _get_observation(self):
        """获取观测状态"""
        pos = self.state[:3]
        vel = self.state[3:6]
        goal_vec = self.goal - pos
        goal_dist = np.linalg.norm(goal_vec)
        
        # 最近障碍物距离（包含静态和动态）
        min_obs_dist = float('inf')
        # 静态障碍物
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, max(0, dist))
        # 动态障碍物
        for obs in self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, max(0, dist))
        
        # 归一化目标方向向量
        if goal_dist > 0:
            goal_direction = goal_vec / goal_dist
        else:
            goal_direction = [0, 0, 0]
        
        observation = np.concatenate([
            pos / 100.0,  # 归一化位置 (3维)
            vel / 5.0,    # 归一化速度 (3维)
            goal_direction,  # 目标方向 (3维)
            [goal_dist / 100.0],  # 归一化目标距离 (1维)
            [min_obs_dist / 20.0],  # 归一化障碍物距离 (1维)
            [len(self.obstacles) / 20.0],  # 静态障碍物数量 (1维)
            [len(self.dynamic_obstacles) / 10.0],  # 动态障碍物数量 (1维)
            [1.0 if self.enable_dynamic_obstacles else 0.0]  # 动态障碍物启用标志 (1维)
        ])
        
        return observation
    
    def step(self, action):
        """环境步进"""
        # 更新动态障碍物
        self._update_dynamic_obstacles()

        # 更新状态
        dt = 0.1
        action = np.array(action, dtype=np.float64)
        self.state[:3] += action * dt  # 位置更新
        self.state[3:6] = action       # 速度更新

        self.step_count += 1

        # 计算奖励
        reward, done, info = self._calculate_reward()

        return self._get_observation(), reward, done, info
    
    def _calculate_reward(self):
        """计算奖励函数"""
        pos = self.state[:3]
        vel = self.state[3:6]
        
        # 基础奖励：接近目标
        goal_dist = np.linalg.norm(pos - self.goal)
        goal_reward = -goal_dist / 100.0
        
        # 到达目标奖励
        if goal_dist < 5.0:
            return 100.0, True, {'success': True, 'reason': 'goal_reached'}
        
        # 碰撞惩罚 - 静态障碍物
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius']:
                return -100.0, True, {'collision': True, 'reason': 'static_collision'}

        # 碰撞惩罚 - 动态障碍物
        for obs in self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius']:
                return -100.0, True, {'collision': True, 'reason': 'dynamic_collision'}
        
        # 边界惩罚
        if (pos < 0).any() or (pos > self.bounds).any():
            return -50.0, True, {'out_of_bounds': True, 'reason': 'out_of_bounds'}
        
        # 速度奖励（鼓励适度移动）
        speed = np.linalg.norm(vel)
        speed_reward = min(speed / 3.0, 1.0) * 0.1
        
        # 安全奖励（距离障碍物的距离）
        min_obs_dist = float('inf')
        # 静态障碍物
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)
        # 动态障碍物
        for obs in self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)

        safety_reward = min(min_obs_dist / 10.0, 1.0) * 0.2
        
        # 时间惩罚（鼓励快速到达）
        time_penalty = -0.01
        
        # 超时
        done = self.step_count >= self.max_steps
        if done:
            info = {'timeout': True, 'reason': 'timeout'}
        else:
            info = {}
        
        total_reward = goal_reward + speed_reward + safety_reward + time_penalty
        
        return total_reward, done, info
    
    def get_state_info(self):
        """获取当前状态信息（用于调试）"""
        pos = self.state[:3]
        vel = self.state[3:6]
        goal_dist = np.linalg.norm(pos - self.goal)
        
        # 计算最近障碍物距离
        min_obs_dist = float('inf')
        closest_obs = None
        for i, obs in enumerate(self.obstacles):
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            if dist < min_obs_dist:
                min_obs_dist = dist
                closest_obs = i
        
        return {
            'position': pos,
            'velocity': vel,
            'goal_distance': goal_dist,
            'min_obstacle_distance': min_obs_dist,
            'closest_obstacle_id': closest_obs,
            'step_count': self.step_count
        }
    
    def render_info(self):
        """打印环境信息（用于调试）"""
        info = self.get_state_info()
        print(f"位置: [{info['position'][0]:.1f}, {info['position'][1]:.1f}, {info['position'][2]:.1f}]")
        print(f"速度: [{info['velocity'][0]:.1f}, {info['velocity'][1]:.1f}, {info['velocity'][2]:.1f}]")
        print(f"目标距离: {info['goal_distance']:.1f}")
        print(f"最近障碍物距离: {info['min_obstacle_distance']:.1f}")
        print(f"步数: {info['step_count']}")
        print("-" * 40)

class EnvironmentVisualizer:
    """简单的环境可视化工具"""
    def __init__(self, env):
        self.env = env
    
    def print_environment_layout(self):
        """打印环境布局"""
        print("=" * 50)
        print("🌍 环境布局")
        print("=" * 50)
        print(f"🏠 起点: [{self.env.start[0]:.1f}, {self.env.start[1]:.1f}, {self.env.start[2]:.1f}]")
        print(f"🎯 终点: [{self.env.goal[0]:.1f}, {self.env.goal[1]:.1f}, {self.env.goal[2]:.1f}]")
        print(f"📏 直线距离: {np.linalg.norm(self.env.goal - self.env.start):.1f}")
        print()
        print("🚧 静态障碍物:")
        for i, obs in enumerate(self.env.obstacles):
            center = obs['center']
            radius = obs['radius']
            print(f"  静态障碍物{i+1}: 中心[{center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}], 半径{radius:.1f}")

        if self.env.enable_dynamic_obstacles and self.env.dynamic_obstacles:
            print("\n🔄 动态障碍物:")
            for i, obs in enumerate(self.env.dynamic_obstacles):
                center = obs['center']
                radius = obs['radius']
                motion_type = obs['motion_type']
                print(f"  动态障碍物{i+1}: 中心[{center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}], 半径{radius:.1f}, 运动类型: {motion_type}")

        print("=" * 50)
    
    def print_trajectory_summary(self, trajectory):
        """打印轨迹摘要"""
        if not trajectory:
            return
        
        print("\n📊 轨迹摘要:")
        print(f"总步数: {len(trajectory)}")
        
        start_pos = trajectory[0]['position']
        end_pos = trajectory[-1]['position']
        total_distance = sum([
            np.linalg.norm(trajectory[i]['position'] - trajectory[i-1]['position'])
            for i in range(1, len(trajectory))
        ])
        
        print(f"起始位置: [{start_pos[0]:.1f}, {start_pos[1]:.1f}, {start_pos[2]:.1f}]")
        print(f"结束位置: [{end_pos[0]:.1f}, {end_pos[1]:.1f}, {end_pos[2]:.1f}]")
        print(f"总飞行距离: {total_distance:.1f}")
        print(f"直线距离: {np.linalg.norm(end_pos - start_pos):.1f}")
        print(f"路径效率: {np.linalg.norm(end_pos - start_pos) / total_distance:.2f}")
        
        # 安全性统计
        min_safety_dist = min([t['min_obstacle_distance'] for t in trajectory])
        print(f"最小安全距离: {min_safety_dist:.1f}")
        
        if trajectory[-1].get('success', False):
            print("✅ 成功到达目标!")
        else:
            print("❌ 未能到达目标")
