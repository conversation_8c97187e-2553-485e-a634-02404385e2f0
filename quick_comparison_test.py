"""
快速对比测试脚本
用于验证自动化对比训练脚本的功能
小规模测试：简单(10+20+10) + 复杂(10+20+10) = 80次训练
"""

import os
import json
import time
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

from train_dwa_rl import train_dwa_rl_model
from environment_config import get_training_stage_config

class QuickComparisonTest:
    """快速对比测试器"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 小规模测试配置
        self.training_config = {
            'stage1_basic': 10,
            'stage2_complex_static': 20, 
            'stage3_dynamic_adaptation': 10
        }
        
        # 创建输出目录
        self.output_dir = f'quick_comparison_test_{self.timestamp}'
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("🧪 快速对比测试")
        print("=" * 50)
        print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎯 测试计划:")
        print(f"  • 简化奖励函数: {sum(self.training_config.values())} episodes")
        print(f"  • 复杂奖励函数: {sum(self.training_config.values())} episodes")
        print(f"  • 总计测试: {sum(self.training_config.values()) * 2} episodes")
        print("=" * 50)
    
    def run_staged_training(self, reward_type='simplified'):
        """执行分阶段训练"""
        print(f"\n🎯 测试 {reward_type.upper()} 奖励函数")
        print("-" * 30)
        
        training_results = {
            'reward_type': reward_type,
            'start_time': datetime.now().isoformat(),
            'stages': {},
            'all_episode_rewards': [],
            'stage_boundaries': [],
            'total_training_time': 0,
            'total_success_count': 0,
            'total_collision_count': 0,
            'total_timeout_count': 0,
            'total_episodes': 0
        }
        
        total_start_time = time.time()
        episode_counter = 0
        
        # 逐阶段训练
        for stage_num, (stage_key, episodes) in enumerate(self.training_config.items(), 1):
            stage_config = get_training_stage_config(stage_key)
            
            print(f"阶段 {stage_num}: {episodes} episodes ({stage_config['environment']})")
            
            stage_start_time = time.time()
            
            try:
                episode_rewards, step_rewards, controller, constraint_data, success_stats = train_dwa_rl_model(
                    num_episodes=episodes,
                    enable_visualization=False,
                    save_outputs=False,
                    environment_config=stage_config['environment'],
                    reward_type=reward_type
                )
                
                stage_time = time.time() - stage_start_time
                
                # 记录数据
                training_results['stage_boundaries'].append(episode_counter + len(episode_rewards))
                training_results['all_episode_rewards'].extend(episode_rewards)
                episode_counter += len(episode_rewards)

                # 累积成功统计
                training_results['total_success_count'] += success_stats['success_count']
                training_results['total_collision_count'] += success_stats['collision_count']
                training_results['total_timeout_count'] += success_stats['timeout_count']
                training_results['total_episodes'] += episodes
                
                # 使用真正的成功统计信息（从环境的success信息获得）
                # 阶段统计
                stage_stats = {
                    'episodes': episodes,
                    'training_time': stage_time,
                    'avg_reward': float(np.mean(episode_rewards)),
                    'success_rate': success_stats['success_rate'],  # 使用真正的成功率
                    'collision_rate': success_stats['collision_rate'],
                    'timeout_rate': success_stats['timeout_rate'],
                    'reward_std': float(np.std(episode_rewards))
                }
                
                training_results['stages'][stage_key] = stage_stats
                training_results['total_training_time'] += stage_time
                
                print(f"  ✅ 完成 - 平均奖励: {stage_stats['avg_reward']:.1f}, 成功率: {stage_stats['success_rate']:.2%}, 碰撞率: {stage_stats['collision_rate']:.2%}")
                
            except Exception as e:
                print(f"  ❌ 失败: {e}")
                training_results['stages'][stage_key] = {'error': str(e)}
                break
        
        training_results['end_time'] = datetime.now().isoformat()
        
        # 整体统计
        if training_results['all_episode_rewards']:
            all_rewards = training_results['all_episode_rewards']

            # 使用真正的成功统计
            training_results['overall_stats'] = {
                'total_episodes': training_results['total_episodes'],
                'overall_success_rate': training_results['total_success_count'] / training_results['total_episodes'],
                'overall_collision_rate': training_results['total_collision_count'] / training_results['total_episodes'],
                'overall_timeout_rate': training_results['total_timeout_count'] / training_results['total_episodes'],
                'overall_avg_reward': float(np.mean(all_rewards)),
                'overall_reward_std': float(np.std(all_rewards)),
                'total_training_time_minutes': training_results['total_training_time'] / 60
            }
        
        print(f"📊 {reward_type.upper()} 总结:")
        print(f"  总episodes: {len(training_results['all_episode_rewards'])}")
        print(f"  总成功率: {training_results['overall_stats']['overall_success_rate']:.3f}")
        print(f"  总训练时间: {training_results['total_training_time']/60:.1f}分钟")
        
        return training_results
    
    def create_individual_chart(self, results, reward_type):
        """创建单个奖励函数的训练图表"""
        print(f"\n🎨 生成 {reward_type.upper()} 训练图表...")

        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))

        episode_rewards = results['all_episode_rewards']
        episodes = range(len(episode_rewards))

        # 1. Episode奖励曲线
        ax1.plot(episodes, episode_rewards, 'b-', alpha=0.8, linewidth=1.5)

        # 添加阶段分界线
        for boundary in results['stage_boundaries']:
            ax1.axvline(x=boundary, color='red', linestyle='--', alpha=0.7)

        ax1.set_xlabel('Episode')
        ax1.set_ylabel('Episode奖励')
        ax1.set_title(f'{reward_type.upper()}奖励函数 - Episode奖励')
        ax1.grid(True, alpha=0.3)

        # 2. 滑动平均
        window = max(5, len(episode_rewards) // 8)
        if window > 1:
            smoothed = np.convolve(episode_rewards, np.ones(window)/window, mode='valid')
            ax2.plot(range(window-1, len(episode_rewards)), smoothed, 'g-', linewidth=2)
        else:
            ax2.plot(episodes, episode_rewards, 'g-', linewidth=2)

        ax2.set_xlabel('Episode')
        ax2.set_ylabel('滑动平均奖励')
        ax2.set_title(f'{reward_type.upper()}奖励函数 - 滑动平均')
        ax2.grid(True, alpha=0.3)

        # 3. 各阶段统计
        stages = ['阶段1', '阶段2', '阶段3']
        stage_keys = ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']

        success_rates = [results['stages'][key]['success_rate'] for key in stage_keys]
        avg_rewards = [results['stages'][key]['avg_reward'] for key in stage_keys]

        x = np.arange(len(stages))
        width = 0.35

        ax3.bar(x - width/2, success_rates, width, label='成功率', color='blue', alpha=0.7)
        ax3_twin = ax3.twinx()
        ax3_twin.bar(x + width/2, avg_rewards, width, label='平均奖励', color='green', alpha=0.7)

        ax3.set_xlabel('训练阶段')
        ax3.set_ylabel('成功率', color='blue')
        ax3_twin.set_ylabel('平均奖励', color='green')
        ax3.set_title(f'{reward_type.upper()}奖励函数 - 阶段统计')
        ax3.set_xticks(x)
        ax3.set_xticklabels(stages)
        ax3.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        chart_path = os.path.join(self.output_dir, f'{reward_type}_training_chart.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ {reward_type.upper()} 训练图表已保存: {chart_path}")
        return chart_path

    def create_quick_visualization(self, simplified_results, complex_results):
        """创建快速可视化"""
        print("\n🎨 生成对比图表...")

        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. Episode奖励对比
        simplified_rewards = simplified_results['all_episode_rewards']
        complex_rewards = complex_results['all_episode_rewards']
        
        ax1.plot(simplified_rewards, 'b-', label='简化奖励函数', alpha=0.8)
        ax1.plot(complex_rewards, 'r-', label='复杂奖励函数', alpha=0.8)
        
        # 添加阶段分界线
        for boundary in simplified_results['stage_boundaries']:
            ax1.axvline(x=boundary, color='gray', linestyle='--', alpha=0.5)
        
        ax1.set_xlabel('Episode')
        ax1.set_ylabel('Episode奖励')
        ax1.set_title('Episode奖励对比')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 成功率对比
        stages = ['阶段1', '阶段2', '阶段3']
        simplified_success = [simplified_results['stages'][key]['success_rate'] 
                            for key in ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']]
        complex_success = [complex_results['stages'][key]['success_rate'] 
                         for key in ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']]
        
        x = np.arange(len(stages))
        width = 0.35
        
        ax2.bar(x - width/2, simplified_success, width, label='简化奖励函数', color='blue', alpha=0.7)
        ax2.bar(x + width/2, complex_success, width, label='复杂奖励函数', color='red', alpha=0.7)
        
        ax2.set_xlabel('训练阶段')
        ax2.set_ylabel('成功率')
        ax2.set_title('各阶段成功率对比')
        ax2.set_xticks(x)
        ax2.set_xticklabels(stages)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 平均奖励对比
        simplified_avg = [simplified_results['stages'][key]['avg_reward'] 
                        for key in ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']]
        complex_avg = [complex_results['stages'][key]['avg_reward'] 
                     for key in ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']]
        
        ax3.bar(x - width/2, simplified_avg, width, label='简化奖励函数', color='blue', alpha=0.7)
        ax3.bar(x + width/2, complex_avg, width, label='复杂奖励函数', color='red', alpha=0.7)
        
        ax3.set_xlabel('训练阶段')
        ax3.set_ylabel('平均奖励')
        ax3.set_title('各阶段平均奖励对比')
        ax3.set_xticks(x)
        ax3.set_xticklabels(stages)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 奖励分布对比
        ax4.hist(simplified_rewards, bins=20, alpha=0.7, label='简化奖励函数', color='blue', density=True)
        ax4.hist(complex_rewards, bins=20, alpha=0.7, label='复杂奖励函数', color='red', density=True)
        
        ax4.set_xlabel('Episode奖励')
        ax4.set_ylabel('密度')
        ax4.set_title('Episode奖励分布对比')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(self.output_dir, 'quick_comparison_chart.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 对比图表已保存: {chart_path}")
        return chart_path
    
    def save_results(self, simplified_results, complex_results):
        """保存结果"""
        print("\n💾 保存测试结果...")
        
        # 保存简化奖励函数结果
        simplified_path = os.path.join(self.output_dir, 'simplified_test_results.json')
        with open(simplified_path, 'w', encoding='utf-8') as f:
            json.dump(simplified_results, f, indent=2, ensure_ascii=False)
        
        # 保存复杂奖励函数结果
        complex_path = os.path.join(self.output_dir, 'complex_test_results.json')
        with open(complex_path, 'w', encoding='utf-8') as f:
            json.dump(complex_results, f, indent=2, ensure_ascii=False)
        
        # 创建对比摘要
        summary = {
            'test_info': {
                'timestamp': self.timestamp,
                'episodes_per_type': sum(self.training_config.values()),
                'training_config': self.training_config
            },
            'simplified_summary': simplified_results['overall_stats'],
            'complex_summary': complex_results['overall_stats'],
            'comparison': {
                'success_rate_diff': simplified_results['overall_stats']['overall_success_rate'] - 
                                   complex_results['overall_stats']['overall_success_rate'],
                'reward_diff': simplified_results['overall_stats']['overall_avg_reward'] - 
                             complex_results['overall_stats']['overall_avg_reward'],
                'time_ratio': simplified_results['overall_stats']['total_training_time_minutes'] / 
                            complex_results['overall_stats']['total_training_time_minutes']
            }
        }
        
        summary_path = os.path.join(self.output_dir, 'test_summary.json')
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试结果已保存到: {self.output_dir}")
        return summary
    
    def run_quick_test(self):
        """运行快速测试"""
        total_start_time = time.time()

        # 1. 测试简化奖励函数
        simplified_results = self.run_staged_training('simplified')

        print("\n" + "="*50)

        # 2. 测试复杂奖励函数
        complex_results = self.run_staged_training('complex')

        # 3. 创建各自的训练图表
        simplified_chart = self.create_individual_chart(simplified_results, 'simplified')
        complex_chart = self.create_individual_chart(complex_results, 'complex')

        # 4. 创建对比可视化
        comparison_chart = self.create_quick_visualization(simplified_results, complex_results)

        # 5. 保存结果
        summary = self.save_results(simplified_results, complex_results)

        total_time = time.time() - total_start_time

        # 6. 打印总结
        print(f"\n🎉 快速测试完成!")
        print("=" * 50)
        print(f"⏱️ 总测试时间: {total_time/60:.1f}分钟")
        print(f"📁 结果保存在: {self.output_dir}")
        print(f"🎨 生成的图表:")
        print(f"  • 简化奖励函数: {simplified_chart}")
        print(f"  • 复杂奖励函数: {complex_chart}")
        print(f"  • 对比图表: {comparison_chart}")

        print(f"\n📊 测试结果摘要:")
        print(f"简化奖励函数: 成功率 {simplified_results['overall_stats']['overall_success_rate']:.3f}, "
              f"平均奖励 {simplified_results['overall_stats']['overall_avg_reward']:.1f}")
        print(f"复杂奖励函数: 成功率 {complex_results['overall_stats']['overall_success_rate']:.3f}, "
              f"平均奖励 {complex_results['overall_stats']['overall_avg_reward']:.1f}")
        print(f"成功率差异: {summary['comparison']['success_rate_diff']:+.3f}")
        print(f"奖励差异: {summary['comparison']['reward_diff']:+.1f}")

        return {
            'simplified': simplified_results,
            'complex': complex_results,
            'summary': summary,
            'simplified_chart': simplified_chart,
            'complex_chart': complex_chart,
            'comparison_chart': comparison_chart
        }

def main():
    """主函数"""
    print("🧪 启动快速对比测试")
    print("📊 小规模验证自动化训练脚本功能")
    
    # 创建测试器
    tester = QuickComparisonTest()
    
    # 运行测试
    results = tester.run_quick_test()
    
    print(f"\n✅ 测试完成! 如果结果正常，可以运行完整版本:")
    print(f"python automated_reward_comparison.py")
    
    return results

if __name__ == "__main__":
    main()
