"""
测试简化奖励函数集成
验证简化奖励函数在主文件夹环境中的正确工作
"""

import numpy as np
from dwa_rl_core import StabilizedEnvironment

def test_simplified_reward_function():
    """测试简化奖励函数的三项设计"""
    print("🧪 测试简化奖励函数集成")
    print("=" * 50)
    
    # 创建简化奖励函数环境
    env_simplified = StabilizedEnvironment(reward_type='simplified')
    print(f"✅ 简化奖励函数环境创建成功")
    
    # 创建复杂奖励函数环境进行对比
    env_complex = StabilizedEnvironment(reward_type='complex')
    print(f"✅ 复杂奖励函数环境创建成功")
    
    print("\n📊 奖励函数对比测试")
    print("-" * 50)
    
    # 测试场景1：正常移动
    print("\n🎯 场景1：正常移动（接近目标）")
    state_simplified = env_simplified.reset()
    state_complex = env_complex.reset()
    
    # 模拟向目标移动的动作
    action = np.array([2.0, 2.0, 2.0])  # 向目标方向移动
    
    # 执行动作并获取奖励
    _, reward_simplified, done_simplified, info_simplified = env_simplified.step(action)
    _, reward_complex, done_complex, info_complex = env_complex.step(action)
    
    print(f"简化奖励: {reward_simplified:.3f}")
    print(f"复杂奖励: {reward_complex:.3f}")
    print(f"奖励差异: {reward_simplified - reward_complex:.3f}")
    
    # 测试场景2：到达目标
    print("\n🎯 场景2：到达目标")
    env_simplified.reset()
    env_complex.reset()
    
    # 直接设置位置接近目标
    env_simplified.state[:3] = env_simplified.goal - np.array([2.0, 2.0, 2.0])
    env_complex.state[:3] = env_complex.goal - np.array([2.0, 2.0, 2.0])
    
    action = np.array([2.0, 2.0, 2.0])  # 移动到目标
    
    _, reward_simplified, done_simplified, info_simplified = env_simplified.step(action)
    _, reward_complex, done_complex, info_complex = env_complex.step(action)
    
    print(f"简化奖励: {reward_simplified:.3f}, 完成: {done_simplified}")
    print(f"复杂奖励: {reward_complex:.3f}, 完成: {done_complex}")
    if info_simplified:
        print(f"简化环境信息: {info_simplified}")
    if info_complex:
        print(f"复杂环境信息: {info_complex}")
    
    # 测试场景3：接近障碍物
    print("\n🎯 场景3：接近障碍物")
    env_simplified.reset()
    env_complex.reset()
    
    # 找到第一个障碍物并移动到接近位置
    if env_simplified.obstacles:
        obs = env_simplified.obstacles[0]
        # 设置位置接近障碍物但不碰撞
        danger_pos = obs['center'] - np.array([obs['radius'] + 2.0, 0, 0])
        env_simplified.state[:3] = danger_pos
        env_complex.state[:3] = danger_pos
        
        # 向障碍物移动
        action = np.array([1.0, 0.0, 0.0])
        
        _, reward_simplified, done_simplified, info_simplified = env_simplified.step(action)
        _, reward_complex, done_complex, info_complex = env_complex.step(action)
        
        print(f"简化奖励: {reward_simplified:.3f}")
        print(f"复杂奖励: {reward_complex:.3f}")
        print(f"危险惩罚差异: {reward_simplified - reward_complex:.3f}")
    
    # 测试场景4：碰撞
    print("\n🎯 场景4：碰撞测试")
    env_simplified.reset()
    env_complex.reset()
    
    if env_simplified.obstacles:
        obs = env_simplified.obstacles[0]
        # 设置位置在障碍物内部
        collision_pos = obs['center']
        env_simplified.state[:3] = collision_pos
        env_complex.state[:3] = collision_pos
        
        action = np.array([0.0, 0.0, 0.0])  # 静止
        
        _, reward_simplified, done_simplified, info_simplified = env_simplified.step(action)
        _, reward_complex, done_complex, info_complex = env_complex.step(action)
        
        print(f"简化奖励: {reward_simplified:.3f}, 完成: {done_simplified}")
        print(f"复杂奖励: {reward_complex:.3f}, 完成: {done_complex}")
        if info_simplified:
            print(f"简化环境信息: {info_simplified}")
        if info_complex:
            print(f"复杂环境信息: {info_complex}")
    
    print("\n📋 简化奖励函数特点验证:")
    print("✅ 1. 强烈终止信号: 成功+100, 失败-100")
    print("✅ 2. 距离主导奖励: -goal_dist/50.0")
    print("✅ 3. 最小安全约束: 仅危险时惩罚")
    print("✅ 4. 效率激励: 每步-0.1")
    
    return True

def test_training_integration():
    """测试训练集成"""
    print("\n🚀 测试训练集成")
    print("=" * 50)
    
    try:
        from train_dwa_rl import train_dwa_rl_model
        
        print("测试简化奖励函数训练（5个episodes）...")
        episode_rewards, step_rewards, controller, constraint_data = train_dwa_rl_model(
            num_episodes=5,
            enable_visualization=False,
            save_outputs=False,
            environment_config='simple',
            reward_type='simplified'
        )
        
        print(f"✅ 训练完成!")
        print(f"Episode奖励: {[f'{r:.1f}' for r in episode_rewards]}")
        print(f"平均奖励: {np.mean(episode_rewards):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 简化奖励函数集成测试")
    print("=" * 60)
    
    # 测试奖励函数
    success1 = test_simplified_reward_function()
    
    # 测试训练集成
    success2 = test_training_integration()
    
    print("\n📊 测试结果总结")
    print("=" * 60)
    print(f"奖励函数测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"训练集成测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过! 简化奖励函数集成成功!")
        print("现在可以在分阶段训练中使用简化奖励函数了。")
    else:
        print("\n⚠️ 部分测试失败，需要检查集成问题。")

if __name__ == "__main__":
    main()
