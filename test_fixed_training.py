"""
测试固定场景训练系统是否能正常运行
运行少量episodes验证系统正常
"""

from fixed_scenario_training import FixedScenarioTraining

def test_short_training():
    """测试短时间训练"""
    print("🧪 测试固定场景训练系统...")
    
    # 创建训练器，使用很少的episodes进行测试
    trainer = FixedScenarioTraining(seed=42)
    
    # 修改训练配置为很少的episodes用于测试
    trainer.training_stages = {
        'stage1': {'episodes': 5, 'scenario': 'stage1', 'description': '阶段1：简单场景训练'},
        'stage2': {'episodes': 5, 'scenario': 'stage2', 'description': '阶段2：复杂静态场景训练'},
        'stage3': {'episodes': 5, 'scenario': 'stage3', 'description': '阶段3：复杂动态场景训练'}
    }
    
    print("🚀 开始测试训练（每阶段只训练5个episodes）...")
    
    try:
        # 只测试简化奖励函数
        results, controller = trainer.train_single_reward_type('simplified')
        print("✅ 简化奖励函数测试成功!")
        print(f"📊 总体成功率: {results['overall_success_rate']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_short_training()
    if success:
        print("\n🎉 测试成功! 现在可以运行完整的4000次训练:")
        print("python fixed_scenario_training.py --reward-type both")
    else:
        print("\n❌ 测试失败，请检查配置")
