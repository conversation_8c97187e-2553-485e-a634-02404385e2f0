"""
快速测试固定场景训练系统
"""

import numpy as np
from fixed_scenario_config import get_fixed_scenario_config
from dwa_rl_core import StabilizedEnvironment

def quick_test():
    """快速测试固定场景"""
    print("🧪 快速测试固定场景...")
    
    # 测试阶段1
    print("\n📍 测试阶段1:")
    scenario_config = get_fixed_scenario_config('stage1')
    print(f"  {scenario_config['description']}")
    
    env = StabilizedEnvironment(
        reward_type='simplified',
        fixed_scenario_config=scenario_config
    )
    
    # 重置环境
    state = env.reset()
    print(f"  环境重置成功")
    print(f"  静态障碍物数量: {len(env.obstacles)}")
    print(f"  动态障碍物数量: {len(env.dynamic_obstacles)}")
    
    # 测试一步
    action = np.array([1.0, 1.0, 0.5])
    next_state, reward, done, info = env.step(action)
    print(f"  执行一步成功，奖励: {reward:.3f}")
    
    # 测试重置一致性
    state1 = env.reset()
    obstacles1_count = len(env.obstacles)
    
    state2 = env.reset()
    obstacles2_count = len(env.obstacles)
    
    if obstacles1_count == obstacles2_count:
        print("  ✅ 重置一致性验证通过")
    else:
        print("  ❌ 重置一致性验证失败")
    
    print("\n🎉 快速测试完成!")

if __name__ == "__main__":
    quick_test()
