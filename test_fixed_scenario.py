"""
测试固定场景训练系统
验证固定场景配置是否正确工作
"""

import numpy as np
from fixed_scenario_config import get_fixed_scenario_config
from dwa_rl_core import StabilizedEnvironment, StabilizedTD3Controller
from td3_config import td3_config

def test_fixed_scenario_environment():
    """测试固定场景环境"""
    print("🧪 测试固定场景环境...")
    
    # 测试各个阶段的场景配置
    for stage in ['stage1', 'stage2', 'stage3']:
        print(f"\n📍 测试 {stage}:")
        
        # 获取固定场景配置
        scenario_config = get_fixed_scenario_config(stage)
        print(f"  场景描述: {scenario_config['description']}")
        
        # 创建环境
        env = StabilizedEnvironment(
            enable_dynamic_obstacles=(len(scenario_config['dynamic_obstacles']) > 0),
            reward_type='simplified',
            fixed_scenario_config=scenario_config
        )
        
        # 测试多次重置，验证场景是否固定
        print("  测试场景一致性...")
        
        # 第一次重置
        state1 = env.reset()
        obstacles1 = [(obs['center'].copy(), obs['radius']) for obs in env.obstacles]
        dynamic1 = [(obs['center'].copy(), obs['radius']) for obs in env.dynamic_obstacles]
        
        # 第二次重置
        state2 = env.reset()
        obstacles2 = [(obs['center'].copy(), obs['radius']) for obs in env.obstacles]
        dynamic2 = [(obs['center'].copy(), obs['radius']) for obs in env.dynamic_obstacles]
        
        # 验证一致性
        obstacles_match = len(obstacles1) == len(obstacles2)
        if obstacles_match:
            for (center1, radius1), (center2, radius2) in zip(obstacles1, obstacles2):
                if not (np.allclose(center1, center2) and abs(radius1 - radius2) < 1e-6):
                    obstacles_match = False
                    break
        
        dynamic_match = len(dynamic1) == len(dynamic2)
        if dynamic_match:
            for (center1, radius1), (center2, radius2) in zip(dynamic1, dynamic2):
                if not (np.allclose(center1, center2) and abs(radius1 - radius2) < 1e-6):
                    dynamic_match = False
                    break
        
        if obstacles_match and dynamic_match:
            print("  ✅ 场景一致性验证通过")
        else:
            print("  ❌ 场景一致性验证失败")
            return False
        
        print(f"  静态障碍物: {len(env.obstacles)}个")
        print(f"  动态障碍物: {len(env.dynamic_obstacles)}个")
        print(f"  起点: {env.start}")
        print(f"  终点: {env.goal}")
    
    print("\n✅ 所有固定场景测试通过!")
    return True

def test_training_consistency():
    """测试训练一致性"""
    print("\n🧪 测试训练一致性...")
    
    # 使用相同的随机种子和场景配置
    np.random.seed(42)
    
    # 获取阶段1场景配置
    scenario_config = get_fixed_scenario_config('stage1')
    
    # 创建两个相同的环境
    env1 = StabilizedEnvironment(
        reward_type='simplified',
        fixed_scenario_config=scenario_config
    )
    
    env2 = StabilizedEnvironment(
        reward_type='simplified',
        fixed_scenario_config=scenario_config
    )
    
    # 创建两个相同的控制器
    controller1 = StabilizedTD3Controller(td3_config)
    controller2 = StabilizedTD3Controller(td3_config)
    
    # 重置随机种子确保一致性
    np.random.seed(42)
    
    # 运行几个episode，验证结果一致性
    print("  运行测试episodes...")
    
    for episode in range(3):
        # 重置环境
        state1 = env1.reset()
        state2 = env2.reset()
        
        # 验证初始状态一致
        if not np.allclose(state1, state2):
            print(f"  ❌ Episode {episode}: 初始状态不一致")
            return False
        
        # 运行几步
        for step in range(5):
            # 获取相同的动作（不添加噪声）
            action1, _, _ = controller1.get_action_with_quality(
                np.concatenate([env1.state, state1[6:]]), 
                env1.goal, env1.obstacles, add_noise=False
            )
            action2, _, _ = controller2.get_action_with_quality(
                np.concatenate([env2.state, state2[6:]]), 
                env2.goal, env2.obstacles, add_noise=False
            )
            
            # 执行动作
            next_state1, reward1, done1, _ = env1.step(action1)
            next_state2, reward2, done2, _ = env2.step(action2)
            
            # 验证结果一致性（允许小的数值误差）
            if not (np.allclose(next_state1, next_state2, atol=1e-6) and 
                   abs(reward1 - reward2) < 1e-6 and done1 == done2):
                print(f"  ❌ Episode {episode}, Step {step}: 执行结果不一致")
                print(f"    状态差异: {np.max(np.abs(next_state1 - next_state2))}")
                print(f"    奖励差异: {abs(reward1 - reward2)}")
                return False
            
            state1 = next_state1
            state2 = next_state2
            
            if done1:
                break
    
    print("  ✅ 训练一致性验证通过")
    return True

def test_reward_function_comparison():
    """测试奖励函数对比"""
    print("\n🧪 测试奖励函数对比...")
    
    # 获取阶段1场景配置
    scenario_config = get_fixed_scenario_config('stage1')
    
    # 创建两种奖励函数的环境
    env_simplified = StabilizedEnvironment(
        reward_type='simplified',
        fixed_scenario_config=scenario_config
    )
    
    env_complex = StabilizedEnvironment(
        reward_type='complex',
        fixed_scenario_config=scenario_config
    )
    
    # 重置环境
    state_s = env_simplified.reset()
    state_c = env_complex.reset()
    
    # 验证环境配置相同
    if not (np.allclose(env_simplified.start, env_complex.start) and
           np.allclose(env_simplified.goal, env_complex.goal) and
           len(env_simplified.obstacles) == len(env_complex.obstacles)):
        print("  ❌ 两种奖励函数的环境配置不一致")
        return False
    
    # 执行相同的动作，比较奖励
    action = np.array([1.0, 1.0, 0.5])
    
    _, reward_s, _, _ = env_simplified.step(action)
    _, reward_c, _, _ = env_complex.step(action)
    
    print(f"  相同动作下的奖励:")
    print(f"    简化奖励: {reward_s:.3f}")
    print(f"    复杂奖励: {reward_c:.3f}")
    print(f"    奖励差异: {abs(reward_s - reward_c):.3f}")
    
    # 奖励应该不同（因为计算方式不同）
    if abs(reward_s - reward_c) < 1e-6:
        print("  ⚠️ 警告: 两种奖励函数给出了相同的奖励值")
    else:
        print("  ✅ 两种奖励函数正确产生了不同的奖励值")
    
    return True

def main():
    """主测试函数"""
    print("🚀 固定场景训练系统测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_fixed_scenario_environment,
        test_training_consistency,
        test_reward_function_comparison
    ]
    
    all_passed = True
    for test_func in tests:
        try:
            if not test_func():
                all_passed = False
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过! 固定场景训练系统工作正常")
        print("\n💡 现在可以运行完整的固定场景对比训练:")
        print("   python fixed_scenario_training.py --reward-type both")
    else:
        print("❌ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
