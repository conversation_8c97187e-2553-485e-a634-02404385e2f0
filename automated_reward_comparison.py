"""
一键自动化奖励函数对比训练脚本
简单奖励函数 vs 复杂奖励函数 完整分阶段训练对比
总计4000次训练：简单(500+1000+500) + 复杂(500+1000+500)
"""

import os
import json
import time
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from collections import deque

from train_dwa_rl import train_dwa_rl_model
from environment_config import TRAINING_STAGES, get_training_stage_config

class AutomatedRewardComparison:
    """自动化奖励函数对比训练器"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 分阶段训练配置 (总计2000次每种)
        self.training_config = {
            'stage1_basic': 500,
            'stage2_complex_static': 1000, 
            'stage3_dynamic_adaptation': 500
        }
        
        # 创建统一输出目录
        self.output_dir = f'reward_comparison_training_{self.timestamp}'
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("🚀 自动化奖励函数对比训练")
        print("=" * 80)
        print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎯 训练计划:")
        print(f"  • 简化奖励函数: {sum(self.training_config.values())} episodes")
        print(f"  • 复杂奖励函数: {sum(self.training_config.values())} episodes")
        print(f"  • 总计训练: {sum(self.training_config.values()) * 2} episodes")
        print("=" * 80)
    
    def run_staged_training(self, reward_type='simplified'):
        """执行完整的分阶段训练"""
        print(f"\n🎯 开始 {reward_type.upper()} 奖励函数分阶段训练")
        print("=" * 60)
        
        # 训练结果记录
        training_results = {
            'reward_type': reward_type,
            'start_time': datetime.now().isoformat(),
            'stages': {},
            'all_episode_rewards': [],
            'all_episode_steps': [],
            'stage_boundaries': [],  # 记录阶段边界
            'total_training_time': 0,
            'total_success_count': 0,
            'total_collision_count': 0,
            'total_timeout_count': 0,
            'total_episodes': 0
        }
        
        total_start_time = time.time()
        episode_counter = 0
        
        # 逐阶段训练
        for stage_num, (stage_key, episodes) in enumerate(self.training_config.items(), 1):
            stage_config = get_training_stage_config(stage_key)
            
            print(f"\n📍 阶段 {stage_num}: {stage_config['description']}")
            print(f"环境: {stage_config['environment']}")
            print(f"Episodes: {episodes}")
            print(f"奖励函数: {reward_type}")
            print("-" * 40)
            
            stage_start_time = time.time()
            
            try:
                # 训练当前阶段
                episode_rewards, step_rewards, controller, constraint_data, success_stats = train_dwa_rl_model(
                    num_episodes=episodes,
                    enable_visualization=False,
                    save_outputs=False,  # 不保存中间结果，统一保存
                    environment_config=stage_config['environment'],
                    reward_type=reward_type
                )
                
                stage_time = time.time() - stage_start_time
                
                # 记录阶段边界
                training_results['stage_boundaries'].append(episode_counter + len(episode_rewards))

                # 累积所有episode数据
                training_results['all_episode_rewards'].extend(episode_rewards)
                training_results['all_episode_steps'].extend([len(step_rewards) // len(episode_rewards)] * len(episode_rewards))
                episode_counter += len(episode_rewards)

                # 累积成功统计
                training_results['total_success_count'] += success_stats['success_count']
                training_results['total_collision_count'] += success_stats['collision_count']
                training_results['total_timeout_count'] += success_stats['timeout_count']
                training_results['total_episodes'] += episodes
                
                # 计算阶段统计（使用真实成功率）
                stage_stats = {
                    'environment': stage_config['environment'],
                    'episodes': episodes,
                    'training_time': stage_time,
                    'episode_rewards': episode_rewards,
                    'final_avg_reward': float(np.mean(episode_rewards[-20:])) if len(episode_rewards) >= 20 else float(np.mean(episode_rewards)),
                    'success_rate': success_stats['success_rate'],  # 使用真实成功率
                    'collision_rate': success_stats['collision_rate'],
                    'timeout_rate': success_stats['timeout_rate'],
                    'reward_std': float(np.std(episode_rewards)),
                    'reward_improvement': self.calculate_improvement(episode_rewards),
                    'avg_steps': float(np.mean([len(step_rewards) // len(episode_rewards)] * len(episode_rewards)))
                }
                
                training_results['stages'][stage_key] = stage_stats
                training_results['total_training_time'] += stage_time
                
                print(f"✅ 阶段 {stage_num} 完成!")
                print(f"   训练时间: {stage_time/60:.1f}分钟")
                print(f"   平均奖励: {stage_stats['final_avg_reward']:.2f}")
                print(f"   成功率: {stage_stats['success_rate']:.2%}")
                print(f"   奖励改善: {stage_stats['reward_improvement']:.3f}")
                
            except Exception as e:
                print(f"❌ 阶段 {stage_num} 训练失败: {e}")
                training_results['stages'][stage_key] = {'error': str(e)}
                break
        
        training_results['end_time'] = datetime.now().isoformat()
        
        # 计算整体统计（使用真实成功率）
        if training_results['all_episode_rewards']:
            all_rewards = training_results['all_episode_rewards']
            training_results['overall_stats'] = {
                'total_episodes': training_results['total_episodes'],
                'overall_success_rate': training_results['total_success_count'] / training_results['total_episodes'],
                'overall_collision_rate': training_results['total_collision_count'] / training_results['total_episodes'],
                'overall_timeout_rate': training_results['total_timeout_count'] / training_results['total_episodes'],
                'overall_avg_reward': float(np.mean(all_rewards)),
                'overall_reward_std': float(np.std(all_rewards)),
                'overall_improvement': self.calculate_improvement(all_rewards),
                'total_training_time_hours': training_results['total_training_time'] / 3600
            }
        
        print(f"\n📊 {reward_type.upper()} 分阶段训练完成!")
        print(f"✅ 总episodes: {len(training_results['all_episode_rewards'])}")
        print(f"✅ 总成功率: {training_results['overall_stats']['overall_success_rate']:.3f}")
        print(f"✅ 总训练时间: {training_results['total_training_time']/3600:.2f}小时")
        
        return training_results
    
    def calculate_improvement(self, episode_rewards):
        """计算学习改善程度"""
        if len(episode_rewards) < 40:
            return 0
        
        first_20 = np.mean(episode_rewards[:20])
        last_20 = np.mean(episode_rewards[-20:])
        
        if first_20 != 0:
            return (last_20 - first_20) / abs(first_20)
        else:
            return 0
    
    def create_individual_training_charts(self, results, reward_type):
        """为单个奖励函数创建训练图表"""
        print(f"\n🎨 生成 {reward_type.upper()} 奖励函数训练图表...")

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建3个子图
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))

        episode_rewards = results['all_episode_rewards']
        episodes = range(len(episode_rewards))

        # 1. Episode奖励曲线
        ax1.plot(episodes, episode_rewards, 'b-', alpha=0.7, linewidth=1)

        # 添加阶段分界线和标签
        for i, boundary in enumerate(results['stage_boundaries']):
            ax1.axvline(x=boundary, color='red', linestyle='--', alpha=0.7)
            if i == 0:
                ax1.text(boundary/2, max(episode_rewards)*0.9, '阶段1\n基础训练',
                        ha='center', fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
            elif i == 1:
                ax1.text((results['stage_boundaries'][0] + boundary)/2, max(episode_rewards)*0.9,
                        '阶段2\n复杂静态', ha='center', fontsize=10,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
            elif i == 2:
                ax1.text((results['stage_boundaries'][1] + boundary)/2, max(episode_rewards)*0.9,
                        '阶段3\n动态适应', ha='center', fontsize=10,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))

        ax1.set_xlabel('Episode')
        ax1.set_ylabel('Episode奖励')
        ax1.set_title(f'{reward_type.upper()}奖励函数 - Episode奖励曲线')
        ax1.grid(True, alpha=0.3)

        # 2. 滑动平均奖励
        window = min(50, len(episode_rewards) // 10)  # 自适应窗口大小
        if window > 1:
            smoothed_rewards = np.convolve(episode_rewards, np.ones(window)/window, mode='valid')
            ax2.plot(range(window-1, len(episode_rewards)), smoothed_rewards, 'g-', linewidth=2)
            ax2.set_title(f'{reward_type.upper()}奖励函数 - {window}Episode滑动平均')
        else:
            ax2.plot(episodes, episode_rewards, 'g-', linewidth=2)
            ax2.set_title(f'{reward_type.upper()}奖励函数 - 奖励趋势')

        ax2.set_xlabel('Episode')
        ax2.set_ylabel('滑动平均奖励')
        ax2.grid(True, alpha=0.3)

        # 3. 各阶段统计对比
        stages = ['阶段1\n基础训练', '阶段2\n复杂静态', '阶段3\n动态适应']
        stage_keys = ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']

        success_rates = [results['stages'][key]['success_rate'] for key in stage_keys]
        avg_rewards = [results['stages'][key]['final_avg_reward'] for key in stage_keys]
        training_times = [results['stages'][key]['training_time']/60 for key in stage_keys]  # 转换为分钟

        x = np.arange(len(stages))
        width = 0.25

        # 创建三个柱状图
        bars1 = ax3.bar(x - width, success_rates, width, label='成功率', color='blue', alpha=0.7)

        # 创建第二个y轴用于平均奖励
        ax3_twin = ax3.twinx()
        bars2 = ax3_twin.bar(x, [r/max(avg_rewards) for r in avg_rewards], width,
                            label='平均奖励(归一化)', color='green', alpha=0.7)

        # 创建第三个y轴用于训练时间
        ax3_twin2 = ax3.twinx()
        ax3_twin2.spines['right'].set_position(('outward', 60))
        bars3 = ax3_twin2.bar(x + width, [t/max(training_times) for t in training_times], width,
                             label='训练时间(归一化)', color='red', alpha=0.7)

        ax3.set_xlabel('训练阶段')
        ax3.set_ylabel('成功率', color='blue')
        ax3_twin.set_ylabel('平均奖励(归一化)', color='green')
        ax3_twin2.set_ylabel('训练时间(归一化)', color='red')
        ax3.set_title(f'{reward_type.upper()}奖励函数 - 各阶段统计')
        ax3.set_xticks(x)
        ax3.set_xticklabels(stages)

        # 添加数值标签
        for i, (sr, ar, tt) in enumerate(zip(success_rates, avg_rewards, training_times)):
            ax3.text(i-width, sr+0.02, f'{sr:.2f}', ha='center', fontsize=8)
            ax3_twin.text(i, (ar/max(avg_rewards))+0.02, f'{ar:.0f}', ha='center', fontsize=8)
            ax3_twin2.text(i+width, (tt/max(training_times))+0.02, f'{tt:.0f}m', ha='center', fontsize=8)

        ax3.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        chart_path = os.path.join(self.output_dir, f'{reward_type}_training_analysis.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ {reward_type.upper()} 训练图表已保存: {chart_path}")
        return chart_path

    def create_comparison_visualizations(self, simplified_results, complex_results):
        """创建对比可视化图表"""
        print("\n🎨 生成对比可视化图表...")

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建大图包含多个子图
        fig = plt.figure(figsize=(20, 16))
        
        # 1. Episode奖励对比
        ax1 = plt.subplot(2, 3, 1)
        simplified_rewards = simplified_results['all_episode_rewards']
        complex_rewards = complex_results['all_episode_rewards']
        
        episodes_s = range(len(simplified_rewards))
        episodes_c = range(len(complex_rewards))
        
        plt.plot(episodes_s, simplified_rewards, 'b-', alpha=0.7, label='简化奖励函数', linewidth=1)
        plt.plot(episodes_c, complex_rewards, 'r-', alpha=0.7, label='复杂奖励函数', linewidth=1)
        
        # 添加阶段分界线
        for i, boundary in enumerate(simplified_results['stage_boundaries']):
            plt.axvline(x=boundary, color='gray', linestyle='--', alpha=0.5)
            if i == 0:
                plt.text(boundary/2, max(max(simplified_rewards), max(complex_rewards))*0.9, 
                        '阶段1\n基础训练', ha='center', fontsize=10)
            elif i == 1:
                plt.text((simplified_results['stage_boundaries'][0] + boundary)/2, 
                        max(max(simplified_rewards), max(complex_rewards))*0.9, 
                        '阶段2\n复杂静态', ha='center', fontsize=10)
            elif i == 2:
                plt.text((simplified_results['stage_boundaries'][1] + boundary)/2, 
                        max(max(simplified_rewards), max(complex_rewards))*0.9, 
                        '阶段3\n动态适应', ha='center', fontsize=10)
        
        plt.xlabel('Episode')
        plt.ylabel('Episode奖励')
        plt.title('Episode奖励对比 - 分阶段训练')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. 滑动平均奖励对比
        ax2 = plt.subplot(2, 3, 2)
        window = 50
        simplified_smooth = np.convolve(simplified_rewards, np.ones(window)/window, mode='valid')
        complex_smooth = np.convolve(complex_rewards, np.ones(window)/window, mode='valid')
        
        plt.plot(range(window-1, len(simplified_rewards)), simplified_smooth, 'b-', label='简化奖励函数', linewidth=2)
        plt.plot(range(window-1, len(complex_rewards)), complex_smooth, 'r-', label='复杂奖励函数', linewidth=2)
        
        plt.xlabel('Episode')
        plt.ylabel('滑动平均奖励')
        plt.title(f'{window}Episode滑动平均奖励对比')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 3. 成功率对比（分阶段）
        ax3 = plt.subplot(2, 3, 3)
        stages = ['阶段1\n基础训练', '阶段2\n复杂静态', '阶段3\n动态适应']
        simplified_success = [simplified_results['stages'][key]['success_rate'] 
                            for key in ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']]
        complex_success = [complex_results['stages'][key]['success_rate'] 
                         for key in ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']]
        
        x = np.arange(len(stages))
        width = 0.35
        
        plt.bar(x - width/2, simplified_success, width, label='简化奖励函数', color='blue', alpha=0.7)
        plt.bar(x + width/2, complex_success, width, label='复杂奖励函数', color='red', alpha=0.7)
        
        plt.xlabel('训练阶段')
        plt.ylabel('成功率')
        plt.title('各阶段成功率对比')
        plt.xticks(x, stages)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 4. 训练时间对比
        ax4 = plt.subplot(2, 3, 4)
        simplified_times = [simplified_results['stages'][key]['training_time']/60 
                          for key in ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']]
        complex_times = [complex_results['stages'][key]['training_time']/60 
                       for key in ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']]
        
        plt.bar(x - width/2, simplified_times, width, label='简化奖励函数', color='blue', alpha=0.7)
        plt.bar(x + width/2, complex_times, width, label='复杂奖励函数', color='red', alpha=0.7)
        
        plt.xlabel('训练阶段')
        plt.ylabel('训练时间 (分钟)')
        plt.title('各阶段训练时间对比')
        plt.xticks(x, stages)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 5. 奖励分布对比
        ax5 = plt.subplot(2, 3, 5)
        plt.hist(simplified_rewards, bins=50, alpha=0.7, label='简化奖励函数', color='blue', density=True)
        plt.hist(complex_rewards, bins=50, alpha=0.7, label='复杂奖励函数', color='red', density=True)
        
        plt.xlabel('Episode奖励')
        plt.ylabel('密度')
        plt.title('Episode奖励分布对比')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 6. 学习改善对比
        ax6 = plt.subplot(2, 3, 6)
        simplified_improvements = [simplified_results['stages'][key]['reward_improvement'] 
                                 for key in ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']]
        complex_improvements = [complex_results['stages'][key]['reward_improvement'] 
                              for key in ['stage1_basic', 'stage2_complex_static', 'stage3_dynamic_adaptation']]
        
        plt.bar(x - width/2, simplified_improvements, width, label='简化奖励函数', color='blue', alpha=0.7)
        plt.bar(x + width/2, complex_improvements, width, label='复杂奖励函数', color='red', alpha=0.7)
        
        plt.xlabel('训练阶段')
        plt.ylabel('学习改善程度')
        plt.title('各阶段学习改善对比')
        plt.xticks(x, stages)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(self.output_dir, 'reward_comparison_analysis.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 对比图表已保存: {chart_path}")
        
        return chart_path
    
    def save_results(self, simplified_results, complex_results, comparison_analysis):
        """保存所有结果"""
        print("\n💾 保存训练结果...")
        
        # 保存简化奖励函数结果
        simplified_path = os.path.join(self.output_dir, 'simplified_reward_results.json')
        with open(simplified_path, 'w', encoding='utf-8') as f:
            json.dump(simplified_results, f, indent=2, ensure_ascii=False)
        
        # 保存复杂奖励函数结果
        complex_path = os.path.join(self.output_dir, 'complex_reward_results.json')
        with open(complex_path, 'w', encoding='utf-8') as f:
            json.dump(complex_results, f, indent=2, ensure_ascii=False)
        
        # 保存对比分析
        analysis_path = os.path.join(self.output_dir, 'comparison_analysis.json')
        with open(analysis_path, 'w', encoding='utf-8') as f:
            json.dump(comparison_analysis, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 简化奖励结果: {simplified_path}")
        print(f"✅ 复杂奖励结果: {complex_path}")
        print(f"✅ 对比分析: {analysis_path}")
    
    def generate_comparison_analysis(self, simplified_results, complex_results):
        """生成详细对比分析"""
        analysis = {
            'experiment_info': {
                'timestamp': self.timestamp,
                'total_episodes_per_type': sum(self.training_config.values()),
                'training_config': self.training_config,
                'comparison_type': 'full_staged_reward_comparison'
            },
            'simplified_summary': simplified_results['overall_stats'],
            'complex_summary': complex_results['overall_stats'],
            'stage_comparisons': {},
            'overall_comparison': {}
        }
        
        # 逐阶段对比
        for stage_key in self.training_config.keys():
            if (stage_key in simplified_results['stages'] and 
                stage_key in complex_results['stages']):
                
                s_stage = simplified_results['stages'][stage_key]
                c_stage = complex_results['stages'][stage_key]
                
                analysis['stage_comparisons'][stage_key] = {
                    'success_rate_improvement': s_stage['success_rate'] - c_stage['success_rate'],
                    'reward_improvement_diff': s_stage['reward_improvement'] - c_stage['reward_improvement'],
                    'training_time_ratio': s_stage['training_time'] / c_stage['training_time'],
                    'reward_stability_improvement': c_stage['reward_std'] - s_stage['reward_std']
                }
        
        # 整体对比
        s_overall = simplified_results['overall_stats']
        c_overall = complex_results['overall_stats']
        
        analysis['overall_comparison'] = {
            'success_rate_improvement': s_overall['overall_success_rate'] - c_overall['overall_success_rate'],
            'reward_improvement_diff': s_overall['overall_improvement'] - c_overall['overall_improvement'],
            'training_time_ratio': s_overall['total_training_time_hours'] / c_overall['total_training_time_hours'],
            'reward_stability_improvement': c_overall['overall_reward_std'] - s_overall['overall_reward_std']
        }
        
        return analysis
    
    def run_complete_comparison(self):
        """运行完整的对比实验"""
        total_start_time = time.time()

        # 1. 训练简化奖励函数
        simplified_results = self.run_staged_training('simplified')

        print("\n" + "="*80)

        # 2. 训练复杂奖励函数
        complex_results = self.run_staged_training('complex')

        # 3. 生成对比分析
        comparison_analysis = self.generate_comparison_analysis(simplified_results, complex_results)

        # 4. 创建各自的训练图表
        simplified_chart = self.create_individual_training_charts(simplified_results, 'simplified')
        complex_chart = self.create_individual_training_charts(complex_results, 'complex')

        # 5. 创建对比可视化图表
        comparison_chart = self.create_comparison_visualizations(simplified_results, complex_results)

        # 6. 保存所有结果
        self.save_results(simplified_results, complex_results, comparison_analysis)

        total_time = time.time() - total_start_time

        # 7. 打印最终总结
        self.print_final_summary(simplified_results, complex_results, comparison_analysis, total_time)

        return {
            'simplified': simplified_results,
            'complex': complex_results,
            'analysis': comparison_analysis,
            'simplified_chart': simplified_chart,
            'complex_chart': complex_chart,
            'comparison_chart': comparison_chart
        }
    
    def print_final_summary(self, simplified_results, complex_results, analysis, total_time):
        """打印最终总结"""
        print(f"\n🎉 完整对比实验完成!")
        print("=" * 80)
        print(f"⏱️ 总实验时间: {total_time/3600:.2f}小时")
        print(f"📁 所有结果保存在: {self.output_dir}")

        print(f"\n📊 生成的图表文件:")
        print(f"  🎯 简化奖励函数训练图表: simplified_training_analysis.png")
        print(f"  🔧 复杂奖励函数训练图表: complex_training_analysis.png")
        print(f"  📈 对比分析图表: reward_comparison_analysis.png")

        s_stats = simplified_results['overall_stats']
        c_stats = complex_results['overall_stats']
        overall = analysis['overall_comparison']

        print(f"\n📊 最终对比结果:")
        print("-" * 60)
        print(f"简化奖励函数:")
        print(f"  • 总成功率: {s_stats['overall_success_rate']:.3f}")
        print(f"  • 平均奖励: {s_stats['overall_avg_reward']:.2f}")
        print(f"  • 学习改善: {s_stats['overall_improvement']:.3f}")
        print(f"  • 训练时间: {s_stats['total_training_time_hours']:.2f}小时")

        print(f"\n复杂奖励函数:")
        print(f"  • 总成功率: {c_stats['overall_success_rate']:.3f}")
        print(f"  • 平均奖励: {c_stats['overall_avg_reward']:.2f}")
        print(f"  • 学习改善: {c_stats['overall_improvement']:.3f}")
        print(f"  • 训练时间: {c_stats['total_training_time_hours']:.2f}小时")

        print(f"\n🏆 对比优势:")
        print(f"  • 成功率改善: {overall['success_rate_improvement']:+.3f}")
        print(f"  • 学习效果改善: {overall['reward_improvement_diff']:+.3f}")
        print(f"  • 训练时间比率: {overall['training_time_ratio']:.2f}")
        print(f"  • 稳定性改善: {overall['reward_stability_improvement']:+.2f}")

        print(f"\n📋 文件清单:")
        print(f"  📄 simplified_reward_results.json - 简化奖励函数详细数据")
        print(f"  📄 complex_reward_results.json - 复杂奖励函数详细数据")
        print(f"  📄 comparison_analysis.json - 对比分析报告")
        print(f"  🖼️ simplified_training_analysis.png - 简化奖励函数训练图表")
        print(f"  🖼️ complex_training_analysis.png - 复杂奖励函数训练图表")
        print(f"  🖼️ reward_comparison_analysis.png - 6合1对比图表")

def main():
    """主函数 - 一键运行完整对比实验"""
    print("🚀 启动自动化奖励函数对比训练")
    print("📊 将进行完整的4000次训练对比实验")
    print("⏱️ 预计需要数小时完成，请耐心等待...")

    # 创建对比训练器
    comparator = AutomatedRewardComparison()

    # 运行完整对比实验
    results = comparator.run_complete_comparison()

    print(f"\n✅ 实验完成! 查看结果:")
    print(f"📁 结果目录: {comparator.output_dir}")
    print(f"🎨 生成的图表:")
    print(f"  • 简化奖励函数训练图表: {results['simplified_chart']}")
    print(f"  • 复杂奖励函数训练图表: {results['complex_chart']}")
    print(f"  • 6合1对比图表: {results['comparison_chart']}")

    return results

if __name__ == "__main__":
    main()
