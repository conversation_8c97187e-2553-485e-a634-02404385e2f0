"""
分阶段训练奖励函数对比脚本
对比复杂奖励函数 vs 简化奖励函数在分阶段训练场景中的效果
基于701_2的对比训练思路，适配主文件夹的分阶段训练环境
"""

import os
import json
import time
import argparse
import numpy as np
from datetime import datetime
from collections import deque

from train_dwa_rl import train_dwa_rl_model
from environment_config import TRAINING_STAGES, get_training_stage_config

class StagedRewardComparison:
    """分阶段训练奖励函数对比器"""
    
    def __init__(self, episodes_per_stage=None, seed=42):
        self.seed = seed
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 默认每阶段的训练episodes数
        self.episodes_per_stage = episodes_per_stage or {
            'stage1_basic': 100,
            'stage2_complex_static': 200, 
            'stage3_dynamic_adaptation': 100
        }
        
        # 创建输出目录
        self.output_dir = f'staged_reward_comparison_{self.timestamp}'
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"🔬 分阶段训练奖励函数对比实验")
        print(f"📅 实验时间: {self.timestamp}")
        print(f"📁 输出目录: {self.output_dir}")
        print("=" * 60)
    
    def train_single_reward_type(self, reward_type='simplified'):
        """训练单个奖励函数类型的完整分阶段过程"""
        print(f"\n🚀 开始分阶段训练 - {reward_type.upper()} 奖励函数")
        print("=" * 50)
        
        # 设置随机种子
        np.random.seed(self.seed)
        
        # 训练结果记录
        training_results = {
            'reward_type': reward_type,
            'start_time': datetime.now().isoformat(),
            'stages': {},
            'overall_performance': {}
        }
        
        total_start_time = time.time()
        
        # 逐阶段训练
        for stage_num, (stage_key, episodes) in enumerate(self.episodes_per_stage.items(), 1):
            stage_config = get_training_stage_config(stage_key)
            
            print(f"\n📍 阶段 {stage_num}: {stage_config['description']}")
            print(f"环境: {stage_config['environment']}")
            print(f"Episodes: {episodes}")
            print(f"奖励函数: {reward_type}")
            
            stage_start_time = time.time()
            
            try:
                # 训练当前阶段
                episode_rewards, step_rewards, controller, constraint_data = train_dwa_rl_model(
                    num_episodes=episodes,
                    enable_visualization=False,
                    save_outputs=False,  # 不保存中间结果
                    environment_config=stage_config['environment'],
                    reward_type=reward_type
                )
                
                stage_time = time.time() - stage_start_time
                
                # 计算阶段统计
                stage_stats = {
                    'environment': stage_config['environment'],
                    'episodes': episodes,
                    'training_time': stage_time,
                    'episode_rewards': episode_rewards,
                    'final_avg_reward': float(np.mean(episode_rewards[-10:])) if len(episode_rewards) >= 10 else float(np.mean(episode_rewards)),
                    'total_episodes': len(episode_rewards),
                    'success_rate': len([r for r in episode_rewards if r > 50]) / len(episode_rewards),  # 假设>50为成功
                    'reward_std': float(np.std(episode_rewards)),
                    'reward_improvement': self.calculate_improvement(episode_rewards),
                    'convergence_episode': self.find_convergence(episode_rewards)
                }
                
                training_results['stages'][stage_key] = stage_stats
                
                print(f"✅ 阶段 {stage_num} 完成")
                print(f"   训练时间: {stage_time:.1f}秒")
                print(f"   平均奖励: {stage_stats['final_avg_reward']:.2f}")
                print(f"   成功率: {stage_stats['success_rate']:.2%}")
                print(f"   奖励改善: {stage_stats['reward_improvement']:.2f}")
                
            except Exception as e:
                print(f"❌ 阶段 {stage_num} 训练失败: {e}")
                training_results['stages'][stage_key] = {'error': str(e)}
                break
        
        total_time = time.time() - total_start_time
        training_results['end_time'] = datetime.now().isoformat()
        training_results['total_training_time'] = total_time
        
        # 计算整体性能
        successful_stages = [s for s in training_results['stages'].values() if 'error' not in s]
        if successful_stages:
            training_results['overall_performance'] = {
                'total_success_rate': np.mean([s['success_rate'] for s in successful_stages]),
                'total_episodes': sum([s['total_episodes'] for s in successful_stages]),
                'avg_reward_improvement': np.mean([s['reward_improvement'] for s in successful_stages]),
                'total_training_time': total_time,
                'stages_completed': len(successful_stages)
            }
        
        # 保存结果
        self.save_results(reward_type, training_results)
        
        print(f"\n📊 {reward_type.upper()} 分阶段训练完成!")
        print(f"✅ 总成功率: {training_results['overall_performance'].get('total_success_rate', 0):.3f}")
        print(f"⏱️ 总训练时间: {total_time:.1f}秒")
        
        return training_results
    
    def calculate_improvement(self, episode_rewards):
        """计算学习改善程度"""
        if len(episode_rewards) < 20:
            return 0
        
        first_10 = np.mean(episode_rewards[:10])
        last_10 = np.mean(episode_rewards[-10:])
        
        if first_10 != 0:
            return (last_10 - first_10) / abs(first_10)
        else:
            return 0
    
    def find_convergence(self, episode_rewards, window=10, threshold=0.1):
        """寻找收敛点"""
        if len(episode_rewards) < window * 2:
            return len(episode_rewards)
        
        for i in range(window, len(episode_rewards) - window):
            current_window = episode_rewards[i:i+window]
            next_window = episode_rewards[i+window:i+2*window]
            
            current_mean = np.mean(current_window)
            next_mean = np.mean(next_window)
            
            if current_mean != 0:
                relative_change = abs(next_mean - current_mean) / abs(current_mean)
                if relative_change < threshold:
                    return i + window
        
        return len(episode_rewards)
    
    def save_results(self, reward_type, results):
        """保存训练结果"""
        results_path = os.path.join(self.output_dir, f'{reward_type}_staged_training_results.json')
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"💾 {reward_type.upper()} 结果已保存: {results_path}")
    
    def run_full_comparison(self):
        """运行完整的对比实验"""
        print("🎯 开始完整的分阶段训练奖励函数对比实验")
        total_episodes = sum(self.episodes_per_stage.values()) * 2
        print(f"📊 总共将训练 {total_episodes} 个episodes")
        print("=" * 60)
        
        # 训练复杂奖励函数
        complex_results = self.train_single_reward_type('complex')
        
        print("\n" + "="*60)
        
        # 训练简化奖励函数
        simplified_results = self.train_single_reward_type('simplified')
        
        # 生成对比分析
        comparison_analysis = self.generate_comparison_analysis(complex_results, simplified_results)
        
        return {
            'complex': complex_results,
            'simplified': simplified_results,
            'comparison': comparison_analysis
        }
    
    def generate_comparison_analysis(self, complex_results, simplified_results):
        """生成对比分析报告"""
        analysis = {
            'experiment_info': {
                'timestamp': self.timestamp,
                'episodes_per_stage': self.episodes_per_stage,
                'random_seed': self.seed,
                'comparison_type': 'staged_training_reward_comparison'
            },
            'complex_results': complex_results['overall_performance'],
            'simplified_results': simplified_results['overall_performance'],
            'stage_by_stage_comparison': {},
            'overall_comparison': {}
        }
        
        # 逐阶段对比
        for stage_key in self.episodes_per_stage.keys():
            if (stage_key in complex_results['stages'] and 
                stage_key in simplified_results['stages'] and
                'error' not in complex_results['stages'][stage_key] and
                'error' not in simplified_results['stages'][stage_key]):
                
                complex_stage = complex_results['stages'][stage_key]
                simplified_stage = simplified_results['stages'][stage_key]
                
                analysis['stage_by_stage_comparison'][stage_key] = {
                    'success_rate_improvement': simplified_stage['success_rate'] - complex_stage['success_rate'],
                    'reward_improvement_diff': simplified_stage['reward_improvement'] - complex_stage['reward_improvement'],
                    'convergence_speed_improvement': complex_stage['convergence_episode'] - simplified_stage['convergence_episode'],
                    'training_time_difference': complex_stage['training_time'] - simplified_stage['training_time']
                }
        
        # 整体对比
        if complex_results['overall_performance'] and simplified_results['overall_performance']:
            complex_overall = complex_results['overall_performance']
            simplified_overall = simplified_results['overall_performance']
            
            analysis['overall_comparison'] = {
                'total_success_rate_improvement': simplified_overall['total_success_rate'] - complex_overall['total_success_rate'],
                'avg_reward_improvement_diff': simplified_overall['avg_reward_improvement'] - complex_overall['avg_reward_improvement'],
                'training_time_difference': complex_overall['total_training_time'] - simplified_overall['total_training_time'],
                'stages_completed_diff': simplified_overall['stages_completed'] - complex_overall['stages_completed']
            }
        
        # 保存分析报告
        analysis_path = os.path.join(self.output_dir, 'staged_comparison_analysis.json')
        with open(analysis_path, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        # 打印分析结果
        self.print_comparison_results(analysis)
        
        return analysis
    
    def print_comparison_results(self, analysis):
        """打印对比结果"""
        print(f"\n📋 分阶段训练奖励函数对比分析报告")
        print("=" * 80)
        
        if 'complex_results' in analysis and 'simplified_results' in analysis:
            complex = analysis['complex_results']
            simplified = analysis['simplified_results']
            overall = analysis['overall_comparison']
            
            print(f"🔧 复杂奖励函数:")
            print(f"  • 总成功率: {complex['total_success_rate']:.3f}")
            print(f"  • 平均改善: {complex['avg_reward_improvement']:.3f}")
            print(f"  • 完成阶段: {complex['stages_completed']}/3")
            print(f"  • 总训练时间: {complex['total_training_time']:.1f}秒")
            
            print(f"\n⚡ 简化奖励函数:")
            print(f"  • 总成功率: {simplified['total_success_rate']:.3f}")
            print(f"  • 平均改善: {simplified['avg_reward_improvement']:.3f}")
            print(f"  • 完成阶段: {simplified['stages_completed']}/3")
            print(f"  • 总训练时间: {simplified['total_training_time']:.1f}秒")
            
            print(f"\n📊 整体对比改善:")
            print(f"  • 成功率改善: {overall['total_success_rate_improvement']:+.3f}")
            print(f"  • 学习效果改善: {overall['avg_reward_improvement_diff']:+.3f}")
            print(f"  • 训练时间节省: {overall['training_time_difference']:+.1f}秒")
            print(f"  • 完成阶段差异: {overall['stages_completed_diff']:+d}")

def main():
    parser = argparse.ArgumentParser(description='分阶段训练奖励函数对比实验')
    parser.add_argument('--episodes-stage1', type=int, default=100, help='阶段1训练episodes数量')
    parser.add_argument('--episodes-stage2', type=int, default=200, help='阶段2训练episodes数量')
    parser.add_argument('--episodes-stage3', type=int, default=100, help='阶段3训练episodes数量')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    args = parser.parse_args()
    
    # 配置每阶段的episodes数
    episodes_config = {
        'stage1_basic': args.episodes_stage1,
        'stage2_complex_static': args.episodes_stage2,
        'stage3_dynamic_adaptation': args.episodes_stage3
    }
    
    # 创建对比器
    comparator = StagedRewardComparison(episodes_per_stage=episodes_config, seed=args.seed)
    
    # 运行完整对比
    results = comparator.run_full_comparison()
    
    print(f"\n🎉 分阶段训练奖励函数对比实验完成!")
    print(f"📁 所有结果已保存到: {comparator.output_dir}")
    
    return results

if __name__ == "__main__":
    main()
