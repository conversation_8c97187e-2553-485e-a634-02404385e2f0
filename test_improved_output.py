"""
测试改进后的输出格式
验证训练进度显示是否更加清晰
"""

from fixed_scenario_training import FixedScenarioTraining

def test_improved_output():
    """测试改进后的输出"""
    print("🧪 测试改进后的输出格式...")
    
    # 创建训练器
    trainer = FixedScenarioTraining(seed=42)
    
    # 修改为适中的episodes数量进行测试
    trainer.training_stages = {
        'stage1': {'episodes': 25, 'scenario': 'stage1', 'description': '阶段1：简单场景训练'},
    }
    
    print("🚀 开始测试（25个episodes，观察输出格式）...")
    
    try:
        # 测试简化奖励函数
        results, controller = trainer.train_single_reward_type('simplified')
        print("✅ 输出格式测试成功!")
        print(f"📊 总体成功率: {results['overall_success_rate']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_improved_output()
    if success:
        print("\n🎉 输出格式改进成功!")
        print("现在的输出特点:")
        print("  • 只在第一个episode显示场景信息")
        print("  • 每10个episode显示简单进度")
        print("  • 每50个episode显示详细统计")
        print("  • 包含成功率、近期表现、平均奖励等关键指标")
        print("\n🚀 可以运行完整训练:")
        print("python fixed_scenario_training.py --reward-type both")
    else:
        print("\n❌ 仍有问题，需要进一步调试")
