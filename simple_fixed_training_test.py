"""
简单的固定场景训练测试
验证基本组件是否正常工作
"""

import numpy as np
from fixed_scenario_config import get_fixed_scenario_config
from dwa_rl_core import StabilizedEnvironment, StabilizedTD3Controller
from td3_dwa_rl_architecture import td3_config

def test_basic_components():
    """测试基本组件"""
    print("🧪 测试基本组件...")
    
    # 1. 测试固定场景配置
    print("\n1. 测试固定场景配置...")
    try:
        scenario_config = get_fixed_scenario_config('stage1')
        print(f"✅ 场景配置获取成功: {scenario_config['description']}")
        print(f"   静态障碍物: {len(scenario_config['static_obstacles'])}个")
        print(f"   动态障碍物: {len(scenario_config['dynamic_obstacles'])}个")
    except Exception as e:
        print(f"❌ 场景配置失败: {e}")
        return False
    
    # 2. 测试环境创建
    print("\n2. 测试环境创建...")
    try:
        env = StabilizedEnvironment(
            reward_type='simplified',
            fixed_scenario_config=scenario_config
        )
        print("✅ 环境创建成功")
    except Exception as e:
        print(f"❌ 环境创建失败: {e}")
        return False
    
    # 3. 测试环境重置
    print("\n3. 测试环境重置...")
    try:
        state = env.reset()
        print(f"✅ 环境重置成功，状态维度: {len(state)}")
        print(f"   障碍物数量: {len(env.obstacles)}")
    except Exception as e:
        print(f"❌ 环境重置失败: {e}")
        return False
    
    # 4. 测试控制器创建
    print("\n4. 测试控制器创建...")
    try:
        controller = StabilizedTD3Controller(td3_config)
        print("✅ 控制器创建成功")
    except Exception as e:
        print(f"❌ 控制器创建失败: {e}")
        return False
    
    # 5. 测试动作获取
    print("\n5. 测试动作获取...")
    try:
        full_state = np.concatenate([env.state, state[6:]])
        action, info, safe_actions = controller.get_action_with_quality(
            full_state, env.goal, env.obstacles, add_noise=False
        )
        print(f"✅ 动作获取成功，动作维度: {len(action)}")
        print(f"   安全动作数量: {len(safe_actions)}")
    except Exception as e:
        print(f"❌ 动作获取失败: {e}")
        return False
    
    # 6. 测试环境步进
    print("\n6. 测试环境步进...")
    try:
        next_state, reward, done, info_step = env.step(action)
        print(f"✅ 环境步进成功")
        print(f"   奖励: {reward:.3f}")
        print(f"   完成: {done}")
    except Exception as e:
        print(f"❌ 环境步进失败: {e}")
        return False
    
    print("\n🎉 所有基本组件测试通过!")
    return True

def test_mini_training():
    """测试迷你训练循环"""
    print("\n🚀 测试迷你训练循环...")
    
    try:
        # 设置随机种子
        np.random.seed(42)
        
        # 获取场景配置
        scenario_config = get_fixed_scenario_config('stage1')
        
        # 创建环境和控制器
        env = StabilizedEnvironment(
            reward_type='simplified',
            fixed_scenario_config=scenario_config
        )
        controller = StabilizedTD3Controller(td3_config)
        
        print("开始迷你训练（3个episodes，每个最多10步）...")
        
        for episode in range(3):
            state = env.reset()
            full_state = np.concatenate([env.state, state[6:]])
            
            episode_reward = 0
            
            for step in range(10):  # 最多10步
                # 获取动作
                action, info, safe_actions = controller.get_action_with_quality(
                    full_state, env.goal, env.obstacles, add_noise=False
                )
                
                # 执行动作
                next_state, reward, done, info_step = env.step(action)
                next_full_state = np.concatenate([env.state, next_state[6:]])
                
                episode_reward += reward
                full_state = next_full_state
                
                if done:
                    break
            
            print(f"  Episode {episode+1}: 奖励={episode_reward:.2f}, 步数={step+1}")
        
        print("✅ 迷你训练循环测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 迷你训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 固定场景训练系统 - 基本功能测试")
    print("=" * 60)
    
    # 测试基本组件
    if not test_basic_components():
        print("\n❌ 基本组件测试失败，请检查配置")
        return
    
    # 测试迷你训练
    if not test_mini_training():
        print("\n❌ 迷你训练测试失败，请检查配置")
        return
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过!")
    print("\n✅ 系统准备就绪，可以运行完整训练:")
    print("   python fixed_scenario_training.py --reward-type both")
    print("\n💡 或者先运行小规模测试:")
    print("   python test_fixed_training.py")

if __name__ == "__main__":
    main()
