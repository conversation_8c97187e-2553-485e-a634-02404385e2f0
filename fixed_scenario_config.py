"""
固定场景配置文件
为确保奖励函数对比的公平性，预定义固定的训练场景
"""

import numpy as np

class FixedScenarioConfig:
    """固定场景配置管理器"""
    
    def __init__(self, seed=42):
        """初始化固定场景配置
        
        Args:
            seed: 随机种子，确保场景生成的可重复性
        """
        self.seed = seed
        np.random.seed(seed)
        
        # 固定起点和终点
        self.start = np.array([10.0, 10.0, 10.0], dtype=np.float64)
        self.goal = np.array([80.0, 80.0, 80.0], dtype=np.float64)
        
        # 生成三个阶段的固定场景
        self._generate_stage_scenarios()
    
    def _generate_stage_scenarios(self):
        """生成三个训练阶段的固定场景"""
        
        # 阶段1：简单场景 - 5个固定障碍物
        self.stage1_obstacles = self._generate_stage1_obstacles()
        
        # 阶段2：复杂静态场景 - 继承阶段1的5个 + 新增10个
        self.stage2_obstacles = self._generate_stage2_obstacles()
        
        # 阶段3：复杂动态场景 - 继承阶段2的15个 + 3个固定动态障碍物
        self.stage3_static_obstacles = self.stage2_obstacles.copy()
        self.stage3_dynamic_obstacles = self._generate_stage3_dynamic_obstacles()
    
    def _generate_stage1_obstacles(self):
        """生成阶段1的5个固定障碍物"""
        obstacles = [
            {'center': np.array([30.0, 30.0, 30.0], dtype=np.float64), 'radius': 6.0},
            {'center': np.array([50.0, 20.0, 40.0], dtype=np.float64), 'radius': 5.5},
            {'center': np.array([40.0, 60.0, 50.0], dtype=np.float64), 'radius': 6.5},
            {'center': np.array([60.0, 40.0, 30.0], dtype=np.float64), 'radius': 5.0},
            {'center': np.array([70.0, 70.0, 60.0], dtype=np.float64), 'radius': 7.0}
        ]
        
        print(f"🎯 阶段1场景: 生成 {len(obstacles)} 个固定静态障碍物")
        return obstacles
    
    def _generate_stage2_obstacles(self):
        """生成阶段2的15个固定障碍物（继承阶段1的5个 + 新增10个）"""
        # 继承阶段1的障碍物
        obstacles = self.stage1_obstacles.copy()
        
        # 新增10个障碍物，形成更复杂的环境
        additional_obstacles = [
            # 走廊障碍物
            {'center': np.array([25.0, 45.0, 35.0], dtype=np.float64), 'radius': 4.0},
            {'center': np.array([55.0, 55.0, 45.0], dtype=np.float64), 'radius': 4.5},
            {'center': np.array([35.0, 25.0, 55.0], dtype=np.float64), 'radius': 3.5},
            
            # 边界障碍物
            {'center': np.array([15.0, 50.0, 40.0], dtype=np.float64), 'radius': 3.0},
            {'center': np.array([85.0, 30.0, 50.0], dtype=np.float64), 'radius': 3.0},
            {'center': np.array([50.0, 15.0, 30.0], dtype=np.float64), 'radius': 3.0},
            {'center': np.array([50.0, 85.0, 70.0], dtype=np.float64), 'radius': 3.0},
            
            # 中间区域障碍物
            {'center': np.array([45.0, 45.0, 25.0], dtype=np.float64), 'radius': 4.0},
            {'center': np.array([65.0, 25.0, 65.0], dtype=np.float64), 'radius': 3.5},
            {'center': np.array([25.0, 65.0, 45.0], dtype=np.float64), 'radius': 4.0}
        ]
        
        obstacles.extend(additional_obstacles)
        
        print(f"🎯 阶段2场景: 继承阶段1的 {len(self.stage1_obstacles)} 个 + 新增 {len(additional_obstacles)} 个 = 总计 {len(obstacles)} 个固定静态障碍物")
        return obstacles
    
    def _generate_stage3_dynamic_obstacles(self):
        """生成阶段3的3个固定动态障碍物"""
        dynamic_obstacles = [
            # 动态障碍物1：线性运动
            {
                'center': np.array([20.0, 40.0, 40.0], dtype=np.float64),
                'radius': 4.0,
                'motion_type': 'linear',
                'motion_params': {
                    'velocity': np.array([1.5, 0.0, 0.5], dtype=np.float64),
                    'bounds': {'x': [15, 75], 'y': [35, 45], 'z': [35, 45]}
                },
                'time': 0.0
            },
            
            # 动态障碍物2：圆周运动
            {
                'center': np.array([50.0, 50.0, 40.0], dtype=np.float64),
                'radius': 3.5,
                'motion_type': 'circular',
                'motion_params': {
                    'center_orbit': np.array([50.0, 50.0, 40.0], dtype=np.float64),
                    'radius_orbit': 12.0,
                    'angular_speed': 0.08,
                    'phase': 0.0
                },
                'time': 0.0
            },
            
            # 动态障碍物3：振荡运动
            {
                'center': np.array([60.0, 30.0, 60.0], dtype=np.float64),
                'radius': 4.0,
                'motion_type': 'oscillating',
                'motion_params': {
                    'center_base': np.array([60.0, 30.0, 60.0], dtype=np.float64),
                    'amplitude': np.array([8.0, 10.0, 6.0], dtype=np.float64),
                    'frequency': np.array([0.05, 0.04, 0.06], dtype=np.float64),
                    'phase': np.array([0.0, np.pi/2, np.pi], dtype=np.float64)
                },
                'time': 0.0
            }
        ]
        
        print(f"🎯 阶段3场景: 继承阶段2的 {len(self.stage2_obstacles)} 个静态障碍物 + 新增 {len(dynamic_obstacles)} 个固定动态障碍物")
        return dynamic_obstacles
    
    def get_stage_config(self, stage):
        """获取指定阶段的场景配置
        
        Args:
            stage: 训练阶段 ('stage1', 'stage2', 'stage3')
            
        Returns:
            dict: 包含该阶段所有障碍物配置的字典
        """
        if stage == 'stage1':
            return {
                'static_obstacles': self.stage1_obstacles,
                'dynamic_obstacles': [],
                'start': self.start,
                'goal': self.goal,
                'description': f"阶段1：简单场景 - {len(self.stage1_obstacles)}个固定静态障碍物"
            }
        elif stage == 'stage2':
            return {
                'static_obstacles': self.stage2_obstacles,
                'dynamic_obstacles': [],
                'start': self.start,
                'goal': self.goal,
                'description': f"阶段2：复杂静态场景 - {len(self.stage2_obstacles)}个固定静态障碍物"
            }
        elif stage == 'stage3':
            return {
                'static_obstacles': self.stage3_static_obstacles,
                'dynamic_obstacles': self.stage3_dynamic_obstacles,
                'start': self.start,
                'goal': self.goal,
                'description': f"阶段3：复杂动态场景 - {len(self.stage3_static_obstacles)}个静态 + {len(self.stage3_dynamic_obstacles)}个动态障碍物"
            }
        else:
            raise ValueError(f"未知的训练阶段: {stage}")
    
    def print_scenario_summary(self):
        """打印场景配置摘要"""
        print("\n" + "="*80)
        print("🎯 固定场景配置摘要")
        print("="*80)
        print(f"🎲 随机种子: {self.seed}")
        print(f"🚀 起点: {self.start}")
        print(f"🎯 终点: {self.goal}")
        print()
        
        for stage in ['stage1', 'stage2', 'stage3']:
            config = self.get_stage_config(stage)
            print(f"📍 {config['description']}")
            if config['dynamic_obstacles']:
                print(f"   └─ 动态障碍物运动类型: {[obs['motion_type'] for obs in config['dynamic_obstacles']]}")
        
        print("="*80)

# 创建全局固定场景配置实例
FIXED_SCENARIO = FixedScenarioConfig(seed=42)

# 导出配置获取函数
def get_fixed_scenario_config(stage):
    """获取固定场景配置的便捷函数"""
    return FIXED_SCENARIO.get_stage_config(stage)

if __name__ == "__main__":
    # 测试场景配置
    FIXED_SCENARIO.print_scenario_summary()
    
    # 测试各阶段配置
    for stage in ['stage1', 'stage2', 'stage3']:
        config = get_fixed_scenario_config(stage)
        print(f"\n测试 {stage}:")
        print(f"  静态障碍物数量: {len(config['static_obstacles'])}")
        print(f"  动态障碍物数量: {len(config['dynamic_obstacles'])}")
