"""
简单的固定场景演示
直接展示固定场景vs随机场景的区别
"""

import numpy as np
import random

class SimpleEnvironment:
    """简化的环境类，用于演示"""
    
    def __init__(self, use_fixed_scenario=False):
        self.use_fixed_scenario = use_fixed_scenario
        self.start = np.array([10.0, 10.0, 10.0])
        self.goal = np.array([80.0, 80.0, 80.0])
        
        # 固定场景配置
        if use_fixed_scenario:
            self.fixed_obstacles = [
                {'center': np.array([30.0, 30.0, 30.0]), 'radius': 6.0},
                {'center': np.array([50.0, 20.0, 40.0]), 'radius': 5.5},
                {'center': np.array([40.0, 60.0, 50.0]), 'radius': 6.5},
                {'center': np.array([60.0, 40.0, 30.0]), 'radius': 5.0},
                {'center': np.array([70.0, 70.0, 60.0]), 'radius': 7.0}
            ]
    
    def reset(self):
        """重置环境"""
        if self.use_fixed_scenario:
            # 使用固定障碍物
            self.obstacles = [obs.copy() for obs in self.fixed_obstacles]
        else:
            # 生成随机障碍物
            self.obstacles = []
            num_obstacles = random.randint(3, 5)
            
            obstacle_centers = [
                [30, 30, 30], [50, 20, 40], [40, 60, 50], 
                [60, 40, 30], [70, 70, 60]
            ]
            
            for i in range(num_obstacles):
                center = np.array(obstacle_centers[i], dtype=np.float64)
                radius = random.uniform(4, 8)
                self.obstacles.append({'center': center, 'radius': radius})
        
        return self.get_state()
    
    def get_state(self):
        """获取状态"""
        return np.concatenate([self.start, [0, 0, 0]])

def demonstrate_fixed_vs_random():
    """演示固定场景vs随机场景"""
    print("🎯 固定场景 vs 随机场景演示")
    print("=" * 60)
    
    # 设置随机种子以便演示
    random.seed(42)
    np.random.seed(42)
    
    print("\n📍 随机场景环境:")
    print("-" * 30)
    env_random = SimpleEnvironment(use_fixed_scenario=False)
    
    for i in range(3):
        env_random.reset()
        print(f"重置 {i+1}: 障碍物数量={len(env_random.obstacles)}")
        for j, obs in enumerate(env_random.obstacles[:2]):  # 只显示前2个
            center = obs['center']
            radius = obs['radius']
            print(f"  障碍物{j+1}: 位置=({center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}), 半径={radius:.1f}")
    
    print("\n📍 固定场景环境:")
    print("-" * 30)
    env_fixed = SimpleEnvironment(use_fixed_scenario=True)
    
    for i in range(3):
        env_fixed.reset()
        print(f"重置 {i+1}: 障碍物数量={len(env_fixed.obstacles)}")
        for j, obs in enumerate(env_fixed.obstacles[:2]):  # 只显示前2个
            center = obs['center']
            radius = obs['radius']
            print(f"  障碍物{j+1}: 位置=({center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}), 半径={radius:.1f}")
    
    print("\n💡 关键观察:")
    print("  • 随机场景: 每次重置障碍物数量和位置都可能不同")
    print("  • 固定场景: 每次重置障碍物配置完全一致")

def demonstrate_training_fairness():
    """演示训练公平性"""
    print("\n🔬 训练公平性演示")
    print("=" * 60)
    
    print("\n场景: 比较两种奖励函数的性能")
    print("-" * 40)
    
    # 模拟在随机场景下的训练
    print("\n❌ 在随机场景下训练 (不公平):")
    env_random = SimpleEnvironment(use_fixed_scenario=False)
    
    for episode in range(3):
        env_random.reset()
        print(f"Episode {episode+1}:")
        print(f"  奖励函数A在场景(障碍物数量={len(env_random.obstacles)})下训练")
        
        # 重新生成场景给奖励函数B
        env_random.reset()
        print(f"  奖励函数B在场景(障碍物数量={len(env_random.obstacles)})下训练")
        print("  → 两个奖励函数面对不同场景，对比不公平!")
    
    # 模拟在固定场景下的训练
    print("\n✅ 在固定场景下训练 (公平):")
    env_fixed = SimpleEnvironment(use_fixed_scenario=True)
    
    for episode in range(3):
        env_fixed.reset()
        obstacle_count = len(env_fixed.obstacles)
        print(f"Episode {episode+1}:")
        print(f"  奖励函数A在场景(障碍物数量={obstacle_count})下训练")
        
        # 重置到相同场景给奖励函数B
        env_fixed.reset()
        print(f"  奖励函数B在场景(障碍物数量={len(env_fixed.obstacles)})下训练")
        print("  → 两个奖励函数面对相同场景，对比公平!")

def demonstrate_progressive_learning():
    """演示渐进式学习"""
    print("\n📚 渐进式学习演示")
    print("=" * 60)
    
    # 模拟三个训练阶段
    stages = [
        {"name": "阶段1", "obstacles": 5, "description": "简单场景"},
        {"name": "阶段2", "obstacles": 15, "description": "复杂静态场景"},
        {"name": "阶段3", "obstacles": 15, "description": "复杂动态场景(+3个动态障碍物)"}
    ]
    
    print("\n渐进式训练计划:")
    print("-" * 30)
    
    for stage in stages:
        print(f"{stage['name']}: {stage['obstacles']}个障碍物 - {stage['description']}")
        print(f"  → 在此固定场景下训练500-1000个episodes")
        print(f"  → 智能体逐步适应更复杂的环境")
    
    print("\n💡 渐进式学习的优势:")
    print("  • 从简单到复杂，符合课程学习原理")
    print("  • 每个阶段场景固定，确保学习效果")
    print("  • 阶段间有继承关系，体现全局优化")

def demonstrate_reproducibility():
    """演示可重现性"""
    print("\n🔄 可重现性演示")
    print("=" * 60)
    
    print("\n使用相同随机种子的固定场景:")
    print("-" * 40)
    
    # 第一次运行
    np.random.seed(42)
    env1 = SimpleEnvironment(use_fixed_scenario=True)
    env1.reset()
    obstacles1 = [(obs['center'].copy(), obs['radius']) for obs in env1.obstacles]
    
    # 第二次运行（重新设置种子）
    np.random.seed(42)
    env2 = SimpleEnvironment(use_fixed_scenario=True)
    env2.reset()
    obstacles2 = [(obs['center'].copy(), obs['radius']) for obs in env2.obstacles]
    
    # 验证一致性
    consistent = True
    for (center1, radius1), (center2, radius2) in zip(obstacles1, obstacles2):
        if not (np.allclose(center1, center2) and abs(radius1 - radius2) < 1e-6):
            consistent = False
            break
    
    print(f"第一次运行: {len(obstacles1)}个障碍物")
    print(f"第二次运行: {len(obstacles2)}个障碍物")
    print(f"配置一致性: {'✅ 完全一致' if consistent else '❌ 不一致'}")
    
    print("\n💡 可重现性的重要性:")
    print("  • 实验结果可以被其他研究者重现")
    print("  • 便于调试和分析训练过程")
    print("  • 确保对比实验的科学性")

def main():
    """主演示函数"""
    print("🚀 固定场景训练系统 - 概念演示")
    print("=" * 80)
    print("解决问题: 每个episode随机生成环境导致的训练和对比问题")
    print("=" * 80)
    
    demonstrate_fixed_vs_random()
    demonstrate_training_fairness()
    demonstrate_progressive_learning()
    demonstrate_reproducibility()
    
    print("\n" + "=" * 80)
    print("🎉 演示完成!")
    print("\n📋 总结 - 固定场景训练的优势:")
    print("  1. 🎯 场景一致性: 每个episode使用相同的障碍物配置")
    print("  2. ⚖️  公平对比: 不同奖励函数在相同环境下训练")
    print("  3. 📈 渐进学习: 从简单到复杂的阶段性训练")
    print("  4. 🔄 可重现性: 固定种子确保实验可重复")
    print("  5. 🧠 有效学习: 智能体能从之前经验中学习")
    
    print("\n🚀 下一步:")
    print("  运行完整的固定场景训练对比:")
    print("  python fixed_scenario_training.py --reward-type both")

if __name__ == "__main__":
    main()
